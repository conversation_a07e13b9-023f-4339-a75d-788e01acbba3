# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个MDAC（马来西亚数字到达卡）相关的项目，包含以下主要组件：

- **MDAC智能表单生成器**: 用于自动填写马来西亚移民局MDAC表单的网页工具
- **Chrome扩展开发环境**: 用于开发浏览器扩展的基础结构
- **MDAC字段分析文档**: 详细分析了MDAC网站的表单字段结构和特性

## 主要文件结构

- `MDAC_AI_Generator.html` - MDAC智能表单脚本生成器主文件
- `MDAC web field.md` - MDAC网站表单字段详细分析文档（1000+行）
- `REGION_FIELD_FIX_SUMMARY.md` - 电话区号字段修复总结文档
- `mdac-chrome-extension/` - Chrome扩展开发目录
  - `icons/` - 扩展图标文件夹
- `backup/` - 备份文件目录
- `icons/` - 项目图标资源

## MDAC表单字段架构

基于分析文档，MDAC表单包含以下核心字段结构：

### 个人信息字段
- **姓名** (`name`) - 最大60字符，仅字母
- **护照号** (`passNo`) - 最大12字符，数字和字符
- **出生日期** (`dob`) - DD/MM/YYYY格式日历控件
- **护照有效期** (`passExpDte`) - DD/MM/YYYY格式日历控件
- **电子邮箱** (`email`) - 最大100字符，需验证
- **确认邮箱** (`confirmEmail`) - 与邮箱匹配验证
- **手机号** (`mobile`) - 最大12字符
- **国籍** (`nationality`) - 280个国家/地区选项
- **性别** (`sex`) - MALE/FEMALE选项
- **电话区号** (`region`) - 国际区号选项

### 旅行信息字段
- **到达日期** (`arrDt`) - 日历控件
- **出发日期** (`depDt`) - 日历控件
- **航班号** (`vesselNm`) - 最大30字符
- **旅行方式** (`trvlMode`) - AIR/LAND/SEA
- **最后登船港** (`embark`) - 国家/地区选项
- **住宿类型** (`accommodationStay`) - HOTEL/RESIDENCE/OTHERS
- **住宿地址** (`accommodationAddress1`, `accommodationAddress2`)
- **州属** (`accommodationState`) - 16个马来西亚州属
- **城市** (`accommodationCity`) - 级联加载，约450个城市
- **邮政编码** (`accommodationPostcode`) - 5位数字

### 重要技术特性
- **防止粘贴/复制** - 多数输入字段禁用粘贴和复制
- **级联下拉菜单** - 州属->城市->邮政编码的级联关系
- **实时验证** - JavaScript客户端验证
- **安全令牌** - 包含`_sourcePage`和`__fp`安全字段

## 已知技术问题和修复

### 电话区号字段修复
- **问题**: 脚本使用`phoneRegion`字段ID，实际网站使用`region`
- **修复**: 更新字段映射，确保脚本与实际网站字段匹配
- **状态**: 已修复，详见`REGION_FIELD_FIX_SUMMARY.md`

## 开发注意事项

### 字段ID映射
- 脚本中的字段ID必须与实际MDAC网站匹配
- 重要：`phoneRegion` → `region`（电话区号字段）

### 字段独立性原则
- 国籍字段和电话区号字段完全独立
- 用户可以选择任意国籍和区号组合
- 避免实现自动同步机制

### 表单验证规则
- 大部分字段使用大写转换（CSS类`uppercase`）
- 字符长度限制严格执行
- 日期字段使用只读属性和日历控件
- 邮箱字段需要格式验证和确认匹配

### Chrome扩展开发
- 图标规格：16x16, 32x32, 48x48, 128x128像素
- 需要创建manifest.json、background.js、content.js等核心文件
- 图标文件已准备目录结构，但需实际PNG文件

## 工具和脚本

### MDAC智能生成器功能
- 自动填表功能
- 字段锁定/解锁机制
- 表单数据生成和导出
- 实时预览和调试

### 测试验证
- 字段存在性检查
- 独立设置流程测试
- 事件触发验证
- 跨浏览器兼容性测试

## 数据规模
- **国家/地区选项**: 280个
- **马来西亚州属**: 16个（13个州+3个联邦直辖区）
- **城市总数**: 约450个
- **最多城市州属**: 霹雳州（88个城市）

## 安全考虑
- 所有用户输入需要验证和清理
- 安全令牌机制保护表单提交
- 防止XSS和CSRF攻击
- 遵循最小权限原则