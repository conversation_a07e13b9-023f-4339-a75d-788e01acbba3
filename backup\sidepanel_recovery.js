// MDAC侧边栏主要功能类
class MDACExtension {
    constructor() {
        this.currentData = {};
        this.isConnected = false;
        this.tabInfo = null;
        // 已锁定字段集合，使用表单元素ID标识，如 passengerName, passportNo 等
        this.lockedFields = new Set();
        this.lockStorageKey = 'mdacFieldLocks';
        // 已选择的图片文件对象数组
        this.selectedFiles = [];
        // 自动解析定时器
        this.autoParseTimer = null;
        this.autoParseDelay = 800; // ms - 缩短延迟时间以提高响应性
        this.lastParsedTextHash = '';
        // 主题配置
        this.themeKey = 'mdacTheme';
        // 联系信息快捷缓存键名
        this.contactKey = 'mdacContact';
        // 视觉模型配置键名，包含默认值
        this.visionConfigKey = 'mdacVisionConfig';
        this.visionConfig = {
            confidenceThreshold: 0.55,
            enableMrzOcr: true,
            enableAddressEnhance: true,
            requireDiffConfirm: true
        };
        this.pendingMergedData = null;
        this.pendingDiff = null;

        // 缓存统计定时器
        this.cacheStatsTimer = null;

        this.init();
    }

    async init() {
        console.log('初始化 MDAC扩展侧边栏组件...');

        try {
            // 绑定事件监听器
            this.bindEvents();

            // 检查连接状态
            await this.checkConnection();

            // 加载保存的数据
            await this.loadSavedData();

            // 加载字段锁定状态
            await this.loadFieldLocks();
            this.applyLockedStateToUI();

            // 加载主题配置
            this.loadTheme();

            // 加载联系信息快捷缓存
            await this.loadContactInfo();
            // 加载视觉模型配置
            await this.loadVisionConfig();

            console.log('✅ 侧边栏组件初始化完成');

            // 启动缓存统计定时器
            this.startCacheStatsLogging();

        } catch (error) {
            console.error('❌ MDAC扩展组件初始化失败:', error);
            console.error('错误堆栈:', error.stack);

            // 显示错误状态
            this.showStatus(`初始化失败: ${error.message}`, 'error');

            // 仍然尝试继续，但某些功能可能不可用
            console.warn('⚠️ 尝试以降级模式继续运行...');
        }
    }

    bindEvents() {
        try {
            // 自动解析监听旅客信息输入框
            const travelerInfoEl = document.getElementById('travelerInfo');
            if (travelerInfoEl) {
                travelerInfoEl.addEventListener('input', () => this.handleAutoParseInput());
                console.log('✅ 自动解析事件已绑定到 travelerInfo');
            } else {
                console.error('❌ 找不到 travelerInfo 元素，自动解析功能将不工作');
            }

            // 绑定按钮事件
            const generateBtn = document.getElementById('generateBtn');
            const fillBtn = document.getElementById('fillBtn');
            const parseBtn = document.getElementById('parseBtn');
            const clearBtn = document.getElementById('clearBtn');

            if (generateBtn) generateBtn.addEventListener('click', () => this.generateAndFillForm());
            if (fillBtn) fillBtn.addEventListener('click', () => this.fillFormOnly());
            if (parseBtn) parseBtn.addEventListener('click', () => this.parseContent());
            if (clearBtn) clearBtn.addEventListener('click', () => this.clearAll());

            // 图片上传事件
            this.initImageUploadEvents();

            // 主题切换
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) themeBtn.addEventListener('click', () => this.toggleTheme());

            console.log('✅ 事件绑定完成');

        } catch (error) {
            console.error('❌ 事件绑定失败:', error);
        }
    }

    initImageUploadEvents() {
        const imageUpload = document.getElementById('imageUpload');
        const dropZone = document.getElementById('dropZone');

        if (imageUpload) {
            imageUpload.addEventListener('change', (e) => this.handleImageSelect(e));
        }

        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                this.handleImageDrop(e);
            });
        }
    }

    async handleAutoParseInput() {
        const travelerInfo = document.getElementById('travelerInfo');
        if (!travelerInfo) return;

        const currentText = travelerInfo.value.trim();
        const currentHash = this.hashString(currentText);

        // 避免重复解析相同内容
        if (currentHash === this.lastParsedTextHash || currentText.length < 10) {
            return;
        }

        // 清除之前的定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
        }

        // 设置新的定时器
        this.autoParseTimer = setTimeout(async () => {
            try {
                this.lastParsedTextHash = currentHash;
                console.log('🚀 自动解析触发...');

                await this.parseTextContent(currentText);

            } catch (error) {
                console.error('❌ 自动解析失败:', error);
            }
        }, this.autoParseDelay);
    }

    async parseTextContent(text) {
        if (!text || text.trim().length < 10) {
            this.showStatus('输入内容太短，无法解析', 'warning');
            return;
        }

        this.showStatus('正在解析文本...', 'loading');

        try {
            // 调用豆包API解析
            const result = await this.callDoubaoAPI(text);

            if (result && result.data) {
                this.currentData = { ...this.currentData, ...result.data };
                this.updateFormFields();
                this.showStatus(`✅ 解析完成，提取到 ${Object.keys(result.data).length} 个字段`, 'success');
            } else {
                this.showStatus('❌ 解析失败：无法提取有效数据', 'error');
            }

        } catch (error) {
            console.error('❌ 文本解析错误:', error);
            this.showStatus(`❌ 解析错误: ${error.message}`, 'error');
        }
    }

    async extractDataFromImages(files) {
        if (!files || files.length === 0) {
            throw new Error('没有选择图片文件');
        }

        this.showStatus(`正在处理 ${files.length} 张图片...`, 'loading');

        try {
            // 使用统一多模态处理器
            const processor = window.UnifiedMultiModalProcessor?.getInstance();
            if (!processor) {
                throw new Error('多模态处理器未加载');
            }

            const result = await processor.processImages(files);

            if (result && result.data) {
                this.currentData = { ...this.currentData, ...result.data };
                this.updateFormFields();
                this.showStatus(`✅ 图片处理完成，提取到 ${Object.keys(result.data).length} 个字段`, 'success');
                return result.data;
            } else {
                throw new Error('图片处理返回无效数据');
            }

        } catch (error) {
            console.error('❌ 图片处理错误:', error);
            this.showStatus(`❌ 图片处理错误: ${error.message}`, 'error');
            throw error;
        }
    }

    async generateAndFillForm() {
        try {
            this.showStatus('正在生成表单数据...', 'loading');

            // 合并文本和图片数据
            let allData = { ...this.currentData };

            // 如果有图片，处理图片
            if (this.selectedFiles.length > 0) {
                const imageData = await this.extractDataFromImages(this.selectedFiles);
                allData = { ...allData, ...imageData };
            }

            // 如果有文本输入，处理文本
            const travelerInfo = document.getElementById('travelerInfo')?.value?.trim();
            if (travelerInfo && travelerInfo.length > 10) {
                await this.parseTextContent(travelerInfo);
                allData = { ...allData, ...this.currentData };
            }

            if (Object.keys(allData).length === 0) {
                this.showStatus('❌ 没有可用数据进行填充', 'error');
                return;
            }

            // 填充表单
            await this.fillForm(allData);

        } catch (error) {
            console.error('❌ 生成表单失败:', error);
            this.showStatus(`❌ 生成失败: ${error.message}`, 'error');
        }
    }

    async fillForm(data) {
        if (!data || Object.keys(data).length === 0) {
            this.showStatus('❌ 没有数据可填充', 'error');
            return;
        }

        try {
            this.showStatus('正在填充表单...', 'loading');

            // 发送消息到content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tab) {
                throw new Error('无法获取当前标签页');
            }

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'fillForm',
                data: data,
                lockedFields: Array.from(this.lockedFields)
            });

            if (response && response.success) {
                this.showStatus(`✅ 表单填充完成，填充了 ${response.filledCount || 0} 个字段`, 'success');
            } else {
                throw new Error(response?.error || '填充失败');
            }

        } catch (error) {
            console.error('❌ 表单填充错误:', error);
            this.showStatus(`❌ 填充错误: ${error.message}`, 'error');
        }
    }

    async callDoubaoAPI(text, images = []) {
        const apiKey = '47a1d437-af1e-4833-abc9-82a97235e236';
        const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

        const messages = [{
            role: 'user',
            content: `请从以下信息中提取MDAC表单字段数据，返回JSON格式：

字段说明：
- name: 姓名
- passportNo: 护照号
- birthDate: 出生日期 (DD/MM/YYYY)
- passportExpiry: 护照有效期 (DD/MM/YYYY)
- email: 邮箱
- mobile: 手机号
- nationality: 国籍代码
- gender: 性别 (MALE/FEMALE)
- arrivalDate: 到达日期
- departureDate: 出发日期
- flightNo: 航班号
- accommodationType: 住宿类型
- address1: 地址1
- state: 州属
- postcode: 邮政编码

信息内容：
${text}`
        }];

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: 'doubao-seed-1-6-flash-250828',
                    messages: messages,
                    temperature: 0.1,
                    max_tokens: 1500
                })
            });

            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status}`);
            }

            const result = await response.json();
            const content = result.choices?.[0]?.message?.content;

            if (content) {
                // 尝试解析JSON
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const data = JSON.parse(jsonMatch[0]);
                    return { data };
                }
            }

            throw new Error('API返回格式错误');

        } catch (error) {
            console.error('❌ API调用错误:', error);
            throw error;
        }
    }

    updateFormFields() {
        // 更新界面上的表单字段显示
        const fields = ['name', 'passportNo', 'birthDate', 'email', 'mobile', 'nationality', 'gender'];

        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element && this.currentData[field]) {
                element.value = this.currentData[field];
            }
        });

        // 更新数据预览
        this.updateDataPreview();
    }

    updateDataPreview() {
        const preview = document.getElementById('dataPreview');
        if (preview) {
            const dataCount = Object.keys(this.currentData).length;
            preview.textContent = `已提取 ${dataCount} 个字段`;
        }
    }

    async checkConnection() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (tab && tab.url && tab.url.includes('malaysia.travel')) {
                this.isConnected = true;
                this.tabInfo = tab;
                this.showStatus('✅ 已连接到MDAC网站', 'success');
            } else {
                this.isConnected = false;
                this.showStatus('⚠️ 请先打开MDAC网站', 'warning');
            }

        } catch (error) {
            this.isConnected = false;
            this.showStatus('❌ 连接检查失败', 'error');
            console.error('连接检查错误:', error);
        }
    }

    async loadSavedData() {
        try {
            const result = await chrome.storage.local.get(['mdacData']);
            if (result.mdacData) {
                this.currentData = result.mdacData;
                this.updateFormFields();
                console.log('✅ 已加载保存的数据');
            }
        } catch (error) {
            console.error('❌ 加载数据失败:', error);
        }
    }

    async saveData() {
        try {
            await chrome.storage.local.set({ mdacData: this.currentData });
            console.log('✅ 数据已保存');
        } catch (error) {
            console.error('❌ 保存数据失败:', error);
        }
    }

    showStatus(message, type = 'info') {
        const statusEl = document.getElementById('status');
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;

            // 自动清除成功和错误消息
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusEl.textContent = '';
                    statusEl.className = 'status';
                }, 3000);
            }
        }
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    // 其他辅助方法...
    async loadFieldLocks() {
        // 加载字段锁定状态
    }

    applyLockedStateToUI() {
        // 应用锁定状态到UI
    }

    loadTheme() {
        // 加载主题配置
    }

    async loadContactInfo() {
        // 加载联系信息
    }

    async loadVisionConfig() {
        // 加载视觉配置
    }

    startCacheStatsLogging() {
        // 启动缓存统计
    }

    toggleTheme() {
        // 切换主题
    }

    handleImageSelect(e) {
        // 处理图片选择
        this.selectedFiles = Array.from(e.target.files);
        this.updateImagePreview();
    }

    handleImageDrop(e) {
        // 处理图片拖放
        this.selectedFiles = Array.from(e.dataTransfer.files);
        this.updateImagePreview();
    }

    updateImagePreview() {
        // 更新图片预览
        const preview = document.getElementById('imagePreview');
        if (preview) {
            preview.textContent = `已选择 ${this.selectedFiles.length} 张图片`;
        }
    }

    clearAll() {
        // 清除所有数据
        this.currentData = {};
        this.selectedFiles = [];
        this.updateFormFields();
        this.updateImagePreview();
        this.showStatus('已清除所有数据', 'info');
    }

    fillFormOnly() {
        // 仅填充表单，不重新解析
        if (Object.keys(this.currentData).length > 0) {
            this.fillForm(this.currentData);
        } else {
            this.showStatus('❌ 没有数据可填充', 'error');
        }
    }

    parseContent() {
        // 手动解析内容
        const travelerInfo = document.getElementById('travelerInfo')?.value?.trim();
        if (travelerInfo) {
            this.parseTextContent(travelerInfo);
        } else if (this.selectedFiles.length > 0) {
            this.extractDataFromImages(this.selectedFiles);
        } else {
            this.showStatus('❌ 请先输入文本或选择图片', 'error');
        }
    }
}

// 初始化扩展
document.addEventListener('DOMContentLoaded', () => {
    window.mdacExtension = new MDACExtension();
});