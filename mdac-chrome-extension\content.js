// Content Script for MDAC Chrome Extension
console.log('🎯 MDAC内容脚本已加载');

// 检查是否在MDAC页面
const isMDACPage = window.location.href.includes('malaysia.travel') ||
                   window.location.href.includes('mdac') ||
                   window.location.href.includes('visa.malaysia.gov.my') ||
                   window.location.href.toLowerCase().includes('digital arrival') ||
                   window.location.href.includes('imigresen-online.imi.gov.my');
console.log('📍 当前页面:', window.location.href, '是否MDAC页面:', isMDACPage);

// 监听来自扩展的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 内容脚本收到消息:', request);
    
    switch (request.action) {
        case 'fillForm':
            handleFillForm(request.data, sendResponse);
            return true; // 保持异步响应
            
        case 'checkPage':
            sendResponse({ 
                success: true, 
                isMDACPage: isMDACPage,
                url: window.location.href,
                title: document.title
            });
            break;
            
        case 'getFormFields':
            getFormFields(sendResponse);
            return true;

        case 'ping':
            sendResponse({
                success: true,
                message: 'Content script is active',
                isMDACPage: isMDACPage,
                url: window.location.href
            });
            break;

        default:
            console.log('❓ 未知消息类型:', request.action);
            sendResponse({ success: false, error: '未知消息类型' });
    }
});

// 处理表单填充
async function handleFillForm(formData, sendResponse) {
    try {
        console.log('🔄 开始填充表单...', formData);
        
        if (!isMDACPage) {
            throw new Error('当前不在MDAC页面，无法填充表单');
        }
        
        // 等待页面完全加载
        await waitForPageLoad();
        
        // 执行表单填充
        const result = await fillMDACForm(formData);
        
        console.log('✅ 表单填充完成:', result);
        sendResponse({ 
            success: true, 
            result: result,
            message: '表单填充成功！'
        });
        
    } catch (error) {
        console.error('❌ 表单填充失败:', error);
        sendResponse({ 
            success: false, 
            error: error.message 
        });
    }
}

// 等待页面加载完成
function waitForPageLoad() {
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', resolve);
        }
    });
}

// 等待函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 日期格式转换函数
function formatDateForMDAC(dateStr) {
    if (!dateStr) return null;

    // 如果已经是DD/MM/YYYY格式，直接返回
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
        return dateStr;
    }

    // 如果是YYYY-MM-DD格式，转换为DD/MM/YYYY
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        const [year, month, day] = dateStr.split('-');
        return `${day}/${month}/${year}`;
    }

    // 尝试解析其他格式
    try {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }
    } catch (error) {
        console.warn('无法解析日期:', dateStr);
    }

    return dateStr; // 如果无法解析，返回原值
}

// 数据转换函数 - 将标准化数据转换为MDAC网站期望的格式
function transformDataForMDAC(data) {
    const transformed = {};

    // 标准化字段名映射和数据转换
    const fieldMappings = {
        // 基本信息
        name: 'name',
        passNo: 'passNo',
        dob: 'dob',
        nationality: 'nationality',
        sex: 'sex',
        passExpDte: 'passExpDte',

        // 联系信息
        email: 'email',
        confirmEmail: 'confirmEmail',
        region: 'region',
        mobile: 'mobile',

        // 旅行信息
        arrDt: 'arrDt',
        depDt: 'depDt',
        vesselNm: 'vesselNm',
        trvlMode: 'trvlMode',
        embark: 'embark',

        // 住宿信息
        accommodationStay: 'accommodationStay',
        accommodationAddress1: 'accommodationAddress1',
        accommodationAddress2: 'accommodationAddress2',
        accommodationState: 'accommodationState',
        accommodationCity: 'accommodationCity',
        accommodationPostcode: 'accommodationPostcode'
    };

    // 旧字段名到新字段名的映射
    const legacyMappings = {
        gender: 'sex',
        birthDate: 'dob',
        passportExpiry: 'passExpDte',
        arrivalDate: 'arrDt',
        departureDate: 'depDt',
        flightNo: 'vesselNm',
        travelMode: 'trvlMode',
        accommodationType: 'accommodationStay',
        address1: 'accommodationAddress1',
        address2: 'accommodationAddress2',
        state: 'accommodationState',
        city: 'accommodationCity',
        postcode: 'accommodationPostcode'
    };

    // 处理所有字段映射
    for (const [standardKey, targetKey] of Object.entries(fieldMappings)) {
        if (data[standardKey] !== undefined && data[standardKey] !== null && data[standardKey] !== '') {
            transformed[targetKey] = data[standardKey];
        }
    }

    // 处理旧字段名映射
    for (const [legacyKey, standardKey] of Object.entries(legacyMappings)) {
        if (data[legacyKey] !== undefined && data[legacyKey] !== null && data[legacyKey] !== '' && !transformed[standardKey]) {
            transformed[standardKey] = data[legacyKey];
        }
    }

    // 性别转换：MALE/FEMALE → 1/2
    if (transformed.sex) {
        switch (transformed.sex.toString().toUpperCase()) {
            case 'MALE':
            case '1':
                transformed.sex = '1';
                break;
            case 'FEMALE':
            case '2':
                transformed.sex = '2';
                break;
        }
    }

    // 国籍转换
    if (transformed.nationality) {
        transformed.nationality = transformed.nationality.toUpperCase();
    }

    // 日期格式处理：确保正确的DD/MM/YYYY格式
    const dateFields = ['dob', 'passExpDte', 'arrDt', 'depDt'];
    dateFields.forEach(field => {
        if (transformed[field]) {
            transformed[field] = formatDateForMDAC(transformed[field]);
        }
    });

    // 住宿类型转换
    if (transformed.accommodationStay) {
        switch (transformed.accommodationStay.toString().toUpperCase()) {
            case 'HOTEL':
            case '01':
                transformed.accommodationStay = '01';
                break;
            case 'RESIDENCE':
            case '02':
                transformed.accommodationStay = '02';
                break;
            case 'OTHERS':
            case '99':
                transformed.accommodationStay = '99'; // 修正：OTHERS 的正确值是 99
                break;
            default:
                transformed.accommodationStay = '01'; // 默认酒店
        }
    }

    // 旅行方式转换
    if (transformed.trvlMode) {
        switch (transformed.trvlMode.toString().toUpperCase()) {
            case 'AIR':
            case '1':
                transformed.trvlMode = '1';
                break;
            case 'LAND':
            case '2':
                transformed.trvlMode = '2';
                break;
            case 'SEA':
            case '3':
                transformed.trvlMode = '3';
                break;
            default:
                transformed.trvlMode = '1'; // 默认飞机
        }
    }

    console.log('📊 数据转换详情:');
    console.log('原始数据:', data);
    console.log('转换后数据:', transformed);

    return transformed;
}

// 填充表单的核心函数
async function fillMDACForm(data) {
    const results = {
        success: [],
        failed: [],
        warnings: []
    };

    console.log('📝 开始填充MDAC表单字段...');

    // 数据转换 - 将标准化数据转换为MDAC网站格式
    const transformedData = transformDataForMDAC(data);
    console.log('🔄 数据转换完成:', transformedData);

    // 定义字段映射 - 更新为匹配传入的数据字段
    const fieldMapping = {
        name: '#name',
        passNo: '#passNo',
        dob: '#dob',
        nationality: '#nationality',
        sex: '#sex',
        passExpDte: '#passExpDte',
        email: '#email',
        confirmEmail: '#confirmEmail',
        mobile: '#mobile',
        arrDt: '#arrDt',
        depDt: '#depDt',
        vesselNm: '#vesselNm',
        trvlMode: '#trvlMode',
        embark: '#embark',
        accommodationStay: '#accommodationStay',
        accommodationAddress1: '#accommodationAddress1',
        accommodationAddress2: '#accommodationAddress2',
        accommodationState: '#accommodationState',
        accommodationPostcode: '#accommodationPostcode'
    };

    // 需要下拉选择的字段
    const selectFields = ['nationality', 'sex', 'accommodationStay', 'trvlMode', 'embark'];

    // 填充基本字段（除了 region 和 accommodationCity，这些需要特殊处理）
    for (const [key, selector] of Object.entries(fieldMapping)) {
        if (transformedData[key] && transformedData[key] !== '') {
            try {
                const isSelect = selectFields.includes(key);
                const success = await fillField(selector, transformedData[key], isSelect);

                if (success) {
                    results.success.push(`${key}: ${transformedData[key]}`);
                    console.log(`✅ 填充 ${key}: ${transformedData[key]}`);
                } else {
                    results.failed.push(`${key}: 字段不存在或填充失败`);
                    console.log(`❌ 填充失败 ${key}: 字段不存在`);
                }

                // 如果是国籍字段，需要等待 region 选项更新
                if (key === 'nationality') {
                    console.log('⏳ 等待国家代码选项更新...');
                    await wait(2000); // 等待 AJAX 更新完成
                }

            } catch (error) {
                results.failed.push(`${key}: ${error.message}`);
                console.error(`❌ 填充错误 ${key}:`, error);
            }
        }
    }

    // 特殊处理：region 字段（在 nationality 之后）
    if (transformedData.region) {
        try {
            console.log('📞 处理国家/地区代码...');
            const success = await fillField('#region', transformedData.region, true);
            if (success) {
                results.success.push(`region: ${transformedData.region}`);
            } else {
                results.failed.push('region: 选择失败');
            }
        } catch (error) {
            results.failed.push(`region: ${error.message}`);
            console.error('❌ Region 填充错误:', error);
        }
    }

    // 特殊处理：州属和城市级联
    if (transformedData.accommodationState) {
        try {
            console.log('🏛️ 处理州属和城市级联...');

            // 等待城市列表加载
            console.log('⏳ 等待城市列表加载...');
            await wait(3000);

            // 优先使用直接传入的城市值
            if (transformedData.accommodationCity) {
                const citySelected = await selectCityByValue(transformedData.accommodationCity);
                if (citySelected) {
                    results.success.push(`accommodationCity: ${citySelected}`);
                } else {
                    // 如果直接选择失败，尝试智能匹配
                    if (transformedData.accommodationAddress1) {
                        const cityFromAddress = await selectCityFromAddress(transformedData.accommodationAddress1);
                        if (cityFromAddress) {
                            results.success.push(`accommodationCity: ${cityFromAddress}`);
                        } else {
                            results.warnings.push('城市选择失败，需要手动选择');
                        }
                    }
                }
            } else if (transformedData.accommodationAddress1) {
                // 如果没有直接的城市值，从地址智能匹配
                const cityFromAddress = await selectCityFromAddress(transformedData.accommodationAddress1);
                if (cityFromAddress) {
                    results.success.push(`accommodationCity: ${cityFromAddress}`);
                } else {
                    results.warnings.push('城市选择失败，需要手动选择');
                }
            }

        } catch (error) {
            results.failed.push(`州属/城市处理失败: ${error.message}`);
            console.error('❌ 州属/城市处理错误:', error);
        }
    }

    // 显示填充结果
    showFillResults(results);

    return results;
}

// 填充单个字段
async function fillField(selector, value, isSelect = false) {
    const element = document.querySelector(selector);
    if (!element) {
        console.log(`⚠️ 字段不存在: ${selector}`);
        return false;
    }

    try {
        // 滚动到字段位置确保可见
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(100);

        if (isSelect) {
            // 处理下拉选择
            console.log(`🔽 设置下拉选择 ${selector}: ${value}`);

            // 尝试直接设置值
            element.value = value;

            // 如果值不存在，尝试查找匹配的选项
            if (element.value !== value) {
                const options = Array.from(element.options);
                const matchedOption = options.find(option =>
                    option.value === value ||
                    option.textContent.includes(value) ||
                    option.value.includes(value) ||
                    option.textContent.toUpperCase().includes(value.toString().toUpperCase())
                );

                if (matchedOption) {
                    element.value = matchedOption.value;
                    console.log(`✅ 找到匹配选项: ${matchedOption.value} - ${matchedOption.textContent.substring(0, 50)}`);
                } else {
                    console.log(`⚠️ 未找到匹配的选项: ${value}`);
                    console.log('可用选项:', options.slice(0, 5).map(o => `${o.value}: ${o.textContent.substring(0, 30)}`));
                    return false;
                }
            }

            // 触发事件
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));

        } else {
            // 处理输入框
            console.log(`📝 设置输入框 ${selector}: ${value}`);

            // 聚焦字段
            element.focus();
            await wait(100);

            // 如果是只读字段，尝试移除只读属性
            if (element.readOnly || element.hasAttribute('readonly')) {
                console.log(`🔓 处理只读字段: ${selector}`);
                element.removeAttribute('readonly');
                element.readOnly = false;
            }

            // 清除现有值
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));

            // 设置新值
            element.value = value;

            // 触发多个事件以确保更新
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));

            // 对于日期字段，尝试触发 datepicker 事件
            if (selector.includes('dob') || selector.includes('Dt') || selector.includes('Dte')) {
                element.dispatchEvent(new Event('changeDate', { bubbles: true }));
            }

            // 验证值是否设置成功
            if (element.value !== value) {
                console.log(`⚠️ 值设置可能不成功: 期望 ${value}, 实际 ${element.value}`);
                // 对于只读字段，尝试直接设置属性
                if (element.hasAttribute('readonly') || element.readOnly) {
                    element.setAttribute('value', value);
                }
            }
        }

        // 等待一下确保事件处理完成
        await wait(300);
        return true;

    } catch (error) {
        console.error(`❌ 填充字段失败 ${selector}:`, error);
        return false;
    }
}

// 直接按值选择城市
async function selectCityByValue(cityValue) {
    const cityElement = document.querySelector('#accommodationCity');
    if (!cityElement) {
        console.log('❌ 城市选择框不存在');
        return null;
    }

    // 等待选项加载
    let attempts = 0;
    while (cityElement.options.length <= 1 && attempts < 10) {
        await wait(500);
        attempts++;
    }

    if (cityElement.options.length <= 1) {
        console.log('⚠️ 城市选项未加载');
        return null;
    }

    // 尝试直接匹配值或文本
    for (let i = 0; i < cityElement.options.length; i++) {
        const option = cityElement.options[i];
        if (option.value === cityValue ||
            option.text.toUpperCase().includes(cityValue.toUpperCase()) ||
            option.text.toUpperCase() === cityValue.toUpperCase()) {

            cityElement.value = option.value;
            cityElement.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✅ 直接选择城市: ${option.text} (${option.value})`);
            return option.text;
        }
    }

    console.log(`⚠️ 未找到匹配的城市: ${cityValue}`);
    return null;
}

// 从地址智能选择城市
async function selectCityFromAddress(address) {
    const cityElement = document.querySelector('#accommodationCity');
    if (!cityElement) {
        console.log('❌ 城市选择框不存在');
        return null;
    }

    // 等待选项加载
    let attempts = 0;
    while (cityElement.options.length <= 1 && attempts < 10) {
        await wait(500);
        attempts++;
    }

    if (cityElement.options.length <= 1) {
        console.log('⚠️ 城市选项未加载');
        return null;
    }

    // 扩展的城市映射
    const cityMapping = {
        'kuala lumpur': 'KUALA LUMPUR',
        'kl': 'KUALA LUMPUR',
        '吉隆坡': 'KUALA LUMPUR',
        'johor bahru': 'JOHOR BAHRU',
        'jb': 'JOHOR BAHRU',
        '新山': 'JOHOR BAHRU',
        'penang': 'GEORGE TOWN',
        '槟城': 'GEORGE TOWN',
        'malacca': 'MELAKA',
        '马六甲': 'MELAKA',
        'ipoh': 'IPOH',
        '怡保': 'IPOH',
        'kota kinabalu': 'KOTA KINABALU',
        '亚庇': 'KOTA KINABALU',
        'kuching': 'KUCHING',
        '古晋': 'KUCHING',
        'shah alam': 'SHAH ALAM',
        'petaling jaya': 'PETALING JAYA',
        'pj': 'PETALING JAYA',
        'subang': 'SUBANG JAYA',
        'klang': 'KLANG',
        'seremban': 'SEREMBAN',
        'melaka': 'MELAKA BANDARAYA BERSEJARAH'
    };

    const addressLower = address.toLowerCase();

    // 尝试匹配城市
    for (const [keyword, cityName] of Object.entries(cityMapping)) {
        if (addressLower.includes(keyword)) {
            // 查找对应的选项
            for (let i = 0; i < cityElement.options.length; i++) {
                const option = cityElement.options[i];
                if (option.text.toUpperCase().includes(cityName) ||
                    option.value.toUpperCase().includes(cityName)) {

                    cityElement.value = option.value;
                    cityElement.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`✅ 智能选择城市: ${option.text} (${option.value})`);
                    return option.text;
                }
            }
        }
    }

    console.log('⚠️ 未能从地址匹配到合适的城市');
    return null;
}

// 显示填充结果
function showFillResults(results) {
    const summary = `
📊 MDAC表单填充完成！

✅ 成功填充 (${results.success.length}个):
${results.success.map(item => `  • ${item}`).join('\n')}

${results.failed.length > 0 ? `❌ 填充失败 (${results.failed.length}个):
${results.failed.map(item => `  • ${item}`).join('\n')}` : ''}

${results.warnings.length > 0 ? `⚠️ 注意事项 (${results.warnings.length}个):
${results.warnings.map(item => `  • ${item}`).join('\n')}` : ''}

💡 请检查填充结果，确认无误后提交表单。
    `;
    
    console.log(summary);
    
    // 在页面上显示通知
    showNotification('表单填充完成！', `成功: ${results.success.length}, 失败: ${results.failed.length}`, 'success');
}

// 显示页面通知
function showNotification(title, message, type = 'info') {
    // 移除已有的通知
    const existingNotification = document.querySelector('.mdac-extension-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `mdac-extension-notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 15px;
        max-width: 350px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        animation: slideIn 0.3s ease-out;
    `;
    
    notification.innerHTML = `
        <div style="font-weight: 600; margin-bottom: 5px;">${title}</div>
        <div style="font-size: 13px; opacity: 0.9;">${message}</div>
        <button onclick="this.parentElement.remove()" style="
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            opacity: 0.7;
        ">×</button>
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// 获取表单字段信息
function getFormFields(sendResponse) {
    const fields = {};
    const selectors = [
        '#name', '#passNo', '#dob', '#nationality', '#sex', '#passExpDte',
        '#email', '#confirmEmail', '#region', '#mobile', '#arrDt', '#depDt',
        '#vesselNm', '#trvlMode', '#embark', '#accommodationStay',
        '#accommodationAddress1', '#accommodationAddress2', 
        '#accommodationState', '#accommodationCity', '#accommodationPostcode'
    ];
    
    selectors.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            fields[selector] = {
                exists: true,
                type: element.type || element.tagName.toLowerCase(),
                value: element.value,
                options: element.tagName.toLowerCase() === 'select' ? 
                    Array.from(element.options).map(opt => ({ value: opt.value, text: opt.text })) : null
            };
        } else {
            fields[selector] = { exists: false };
        }
    });
    
    sendResponse({ success: true, fields: fields });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 MDAC页面DOM加载完成');
    
    // 发送页面就绪消息给扩展
    chrome.runtime.sendMessage({
        action: 'pageReady',
        url: window.location.href,
        isMDACPage: isMDACPage
    }).catch(error => {
        console.log('⚠️ 发送页面就绪消息失败:', error.message);
    });
});

console.log('✅ MDAC内容脚本初始化完成');
