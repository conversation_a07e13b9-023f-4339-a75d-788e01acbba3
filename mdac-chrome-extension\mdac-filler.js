// 专用的MDAC表单填充脚本
// 这个脚本会被注入到MDAC页面中执行自动填充

(function() {
    'use strict';
    
    console.log('🚀 MDAC自动填充脚本开始执行...');
    
    // 获取传入的数据
    const formData = window.mdacFormData || {};
    
    // 等待函数
    const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    
    // 显示状态通知
    function showStatus(message, type = 'info') {
        // 移除已有通知
        const existing = document.querySelector('.mdac-auto-fill-notification');
        if (existing) existing.remove();
        
        // 创建新通知
        const notification = document.createElement('div');
        notification.className = 'mdac-auto-fill-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
            border-radius: 8px;
            padding: 12px 16px;
            max-width: 300px;
            z-index: 10000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: slideInRight 0.3s ease-out;
        `;
        
        notification.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 4px;">MDAC自动填充</div>
            <div>${message}</div>
        `;
        
        // 添加动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(notification);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    // 安全的字段填充函数
    async function fillField(selector, value, isSelect = false) {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                console.log(`⚠️ 字段不存在: ${selector}`);
                return false;
            }
            
            // 滚动到字段位置确保可见
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await wait(200);
            
            // 聚焦字段
            element.focus();
            await wait(100);
            
            if (isSelect) {
                // 处理下拉选择
                const option = Array.from(element.options).find(opt => 
                    opt.value === value || opt.text.includes(value)
                );
                
                if (option) {
                    element.value = option.value;
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`✅ 选择 ${selector}: ${option.text} (${option.value})`);
                } else {
                    console.log(`⚠️ 选项不存在 ${selector}: ${value}`);
                    return false;
                }
            } else {
                // 处理输入框
                element.value = value;
                
                // 触发所有可能的事件
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.dispatchEvent(new Event('blur', { bubbles: true }));
                element.dispatchEvent(new Event('keyup', { bubbles: true }));
                
                console.log(`✅ 填充 ${selector}: ${value}`);
            }
            
            // 短暂等待确保事件处理完成
            await wait(200);
            return true;
            
        } catch (error) {
            console.error(`❌ 填充字段出错 ${selector}:`, error);
            return false;
        }
    }
    
    // 智能城市选择
    async function selectCityFromAddress(address) {
        const cityElement = document.querySelector('#accommodationCity');
        if (!cityElement) {
            console.log('❌ 城市选择框不存在');
            return null;
        }
        
        // 等待城市列表加载，最多尝试10次
        let attempts = 0;
        while (cityElement.options.length <= 1 && attempts < 10) {
            console.log(`⏳ 等待城市列表加载... (${attempts + 1}/10)`);
            await wait(1000);
            attempts++;
        }
        
        if (cityElement.options.length <= 1) {
            console.log('⚠️ 城市列表未加载或为空');
            return null;
        }
        
        // 城市关键词映射
        const cityMapping = {
            'kuala lumpur': 'KUALA LUMPUR',
            'kl': 'KUALA LUMPUR', 
            '吉隆坡': 'KUALA LUMPUR',
            'johor bahru': 'JOHOR BAHRU',
            'jb': 'JOHOR BAHRU',
            '新山': 'JOHOR BAHRU',
            'penang': 'GEORGE TOWN',
            '槟城': 'GEORGE TOWN',
            'malacca': 'MELAKA',
            '马六甲': 'MELAKA',
            'ipoh': 'IPOH',
            '怡保': 'IPOH',
            'kota kinabalu': 'KOTA KINABALU',
            '亚庇': 'KOTA KINABALU',
            'kuching': 'KUCHING',
            '古晋': 'KUCHING',
            'shah alam': 'SHAH ALAM',
            'petaling jaya': 'PETALING JAYA',
            'pj': 'PETALING JAYA',
            'subang': 'SUBANG JAYA',
            'klang': 'KLANG',
            'seremban': 'SEREMBAN',
            'melaka': 'MELAKA BANDARAYA BERSEJARAH'
        };
        
        const addressLower = address.toLowerCase();
        
        // 尝试匹配城市
        for (const [keyword, cityName] of Object.entries(cityMapping)) {
            if (addressLower.includes(keyword)) {
                // 查找对应的选项
                for (let i = 0; i < cityElement.options.length; i++) {
                    const option = cityElement.options[i];
                    if (option.text.toUpperCase().includes(cityName) || 
                        option.value.toUpperCase().includes(cityName)) {
                        
                        cityElement.value = option.value;
                        cityElement.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`✅ 智能选择城市: ${option.text} (${option.value})`);
                        return option.text;
                    }
                }
            }
        }
        
        console.log('⚠️ 未能匹配到合适的城市，需手动选择');
        return null;
    }
    
    // 主要填充函数
    async function executeFillForm() {
        const results = {
            success: [],
            failed: [],
            warnings: []
        };
        
        showStatus('开始自动填充表单...', 'info');
        
        try {
            console.log('📝 开始执行MDAC表单填充...');
            console.log('📊 表单数据:', formData);
            
            // 字段映射（确保与MDAC网站字段匹配）
            const fieldMapping = {
                name: '#name',
                passNo: '#passNo', 
                dob: '#dob',
                nationality: '#nationality',
                sex: '#sex',
                passExpDte: '#passExpDte',
                email: '#email',
                confirmEmail: '#confirmEmail',
                region: '#region',
                mobile: '#mobile',
                arrDt: '#arrDt',
                depDt: '#depDt',
                vesselNm: '#vesselNm',
                trvlMode: '#trvlMode',
                embark: '#embark',
                accommodationStay: '#accommodationStay',
                accommodationAddress1: '#accommodationAddress1',
                accommodationAddress2: '#accommodationAddress2',
                accommodationState: '#accommodationState',
                accommodationPostcode: '#accommodationPostcode'
            };
            
            // 需要下拉选择的字段
            const selectFields = ['nationality', 'sex', 'region', 'trvlMode', 'embark', 'accommodationStay', 'accommodationState'];
            
            // 逐个填充字段
            for (const [key, selector] of Object.entries(fieldMapping)) {
                if (formData[key] && formData[key] !== '') {
                    const isSelect = selectFields.includes(key);
                    const success = await fillField(selector, formData[key], isSelect);
                    
                    if (success) {
                        results.success.push(`${key}: ${formData[key]}`);
                    } else {
                        results.failed.push(`${key}: 填充失败`);
                    }
                    
                    // 每个字段间稍作停顿，避免过快操作
                    await wait(300);
                }
            }
            
            // 特殊处理：州属选择后的城市级联
            if (formData.accommodationState) {
                console.log('🏛️ 处理州属和城市级联...');
                showStatus('正在处理州属和城市选择...', 'info');
                
                // 确保州属已选择
                await fillField('#accommodationState', formData.accommodationState, true);
                results.success.push(`accommodationState: ${formData.accommodationState}`);
                
                // 等待城市列表加载
                console.log('⏳ 等待城市列表更新...');
                await wait(3000);
                
                // 智能选择城市
                if (formData.accommodationAddress1) {
                    const citySelected = await selectCityFromAddress(formData.accommodationAddress1);
                    if (citySelected) {
                        results.success.push(`accommodationCity: ${citySelected}`);
                    } else {
                        results.warnings.push('城市需要手动选择');
                    }
                }
            }
            
            // 最终验证填充结果
            console.log('🔍 验证填充结果...');
            let actuallyFilled = 0;
            for (const [key, selector] of Object.entries(fieldMapping)) {
                const element = document.querySelector(selector);
                if (element && element.value) {
                    actuallyFilled++;
                }
            }
            
            // 显示最终结果
            const successCount = results.success.length;
            const failedCount = results.failed.length;
            const warningCount = results.warnings.length;
            
            console.log(`\n📊 填充完成统计:`);
            console.log(`✅ 成功填充: ${successCount} 个字段`);
            console.log(`❌ 填充失败: ${failedCount} 个字段`);
            console.log(`⚠️ 警告信息: ${warningCount} 个`);
            console.log(`🔍 实际填充: ${actuallyFilled} 个字段有值`);
            
            if (successCount > 0) {
                showStatus(`填充成功！已填充 ${successCount} 个字段${warningCount > 0 ? '，有' + warningCount + '个需要手动处理' : ''}`, 'success');
            } else {
                showStatus('填充失败，请检查页面和数据', 'error');
            }
            
            // 详细日志
            if (results.success.length > 0) {
                console.log('\n✅ 成功填充的字段:');
                results.success.forEach(item => console.log(`  • ${item}`));
            }
            
            if (results.failed.length > 0) {
                console.log('\n❌ 填充失败的字段:');
                results.failed.forEach(item => console.log(`  • ${item}`));
            }
            
            if (results.warnings.length > 0) {
                console.log('\n⚠️ 需要注意的问题:');
                results.warnings.forEach(item => console.log(`  • ${item}`));
            }
            
            console.log('\n💡 请检查填充结果，确认无误后提交表单。');
            
            return {
                success: true,
                results: results,
                summary: {
                    total: Object.keys(fieldMapping).length,
                    successful: successCount,
                    failed: failedCount,
                    warnings: warningCount,
                    actuallyFilled: actuallyFilled
                }
            };
            
        } catch (error) {
            console.error('❌ 填充过程出现错误:', error);
            showStatus(`填充出错: ${error.message}`, 'error');
            
            return {
                success: false,
                error: error.message,
                results: results
            };
        }
    }
    
    // 执行填充并返回结果
    if (Object.keys(formData).length > 0) {
        executeFillForm().then(result => {
            console.log('🎉 MDAC自动填充完成!', result);
            
            // 将结果存储到全局变量供扩展获取
            window.mdacFillResult = result;
            
            // 触发自定义事件通知扩展
            window.dispatchEvent(new CustomEvent('mdacFillComplete', {
                detail: result
            }));
        }).catch(error => {
            console.error('💥 MDAC填充执行失败:', error);
            window.mdacFillResult = { success: false, error: error.message };
        });
    } else {
        console.warn('⚠️ 没有提供表单数据，跳过填充');
        showStatus('没有数据需要填充', 'error');
    }
    
})();
