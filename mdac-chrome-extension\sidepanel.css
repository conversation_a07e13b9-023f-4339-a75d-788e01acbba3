/* MDAC侧边栏样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
/*\n  用途: MDAC Chrome 扩展侧边栏样式(响应式 + 模块化)\n  作者: 开发团队(基于 CSS)\n  技术栈: 现代 CSS(兼容新 Chrome，使用 :has 等)\n  相关文件: sidepanel.html, sidepanel.js\n  核心功能:\n    - .container.layout-fill 实现自适应双栏布局\n    - .main-content 使用 CSS Grid 栅格系统\n    - 模块化(设置/输入/表单)响应式设计\n*/"\n
}

/* 主题配色 */
:root {
    --color-bg:#f8f9fa;
    --color-surface:#ffffff;
    --color-surface-alt:#f1f3f5;
    --color-border:#dee2e6;
    --color-text:#333;
    --color-text-muted:#6c757d;
    --color-accent:#4facfe;
    --color-accent-alt:#00f2fe;
    --color-danger:#e74c3c;
    --color-success:#2ecc71;
    --color-code-bg:#2c3e50;
}
.theme-dark {
    --color-bg:#1e1f24;
    --color-surface:#262830;
    --color-surface-alt:#2f3138;
    --color-border:#3a3d45;
    --color-text:#ebedf0;
    --color-text-muted:#9aa0ae;
    --color-accent:#4facfe;
    --color-accent-alt:#00c2fe;
    --color-danger:#ff5959;
    --color-success:#4dbf88;
    --color-code-bg:#14181d;
}

body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.4;
        color: var(--color-text);
        background: var(--color-bg);
        font-size: 13px;
        width: 100%;
        height: 100vh;
        overflow-y: hidden; /* use internal scroll in main content for better fit */
}

.container { width:100%; background:var(--color-surface); min-height:100vh; display:flex; flex-direction:column; }

/* 头部样式 */
.header {
    background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-alt) 100%);
    color: #fff;
    padding: 4px 8px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.header h1 {
    font-size: 0.9em;
    margin: 0;
    font-weight: 600;
}


.status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.7em;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ffc107;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #28a745;
}

.status-dot.disconnected {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 快速链接 */
.quick-links {
    padding: 4px 8px;
    text-align: center;
    background: rgba(79, 172, 254, 0.05);
    border-bottom: 1px solid #e9ecef;
}

.mdac-link { display:inline-block; padding:3px 8px; background:linear-gradient(135deg,var(--color-accent) 0%, var(--color-accent-alt) 100%); color:#fff;
    text-decoration: none;
    border-radius: 16px;
    font-size: 0.75em;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.mdac-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
    text-decoration: none;
    color: white;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 0;
}

.section {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

/* Section Container 专门样式 - 用于 Flex 垂直贴合布局 */
.section-container {
    padding: 6px 8px;
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface);
}

.section-container:last-child {
    border-bottom: none; /* 最后一个不需要底边框 */
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.section-header h3 {
    margin: 0;
    font-size: 0.9em;
}

.beta-tag {
    background: #ffc107;
    color: #333;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 8px;
}

.section h3 {
    font-size: 1.1em;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #4facfe;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section h4 {
    font-size: 1em;
    color: #495057;
    margin: 20px 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #dee2e6;
}

/* 表单样式 */
.form-container {
    max-width: 100%;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
    font-size: 0.85em;
}

.form-group input,
.form-group select,
.form-group textarea { width:100%; padding:8px 10px; border:1px solid var(--color-border); border-radius:6px; font-size:13px; transition:all .3s ease; background:var(--color-surface); color:var(--color-text);} 

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus { outline:none; border-color:var(--color-accent); box-shadow:0 0 0 3px rgba(79,172,254,.25); }

/* 按钮样式 */
.input-group {
    position: relative;
}

.input-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    line-height: 1.4;
}

/* travelerInfo 专用样式 - 确保完全贴合容器 */
#travelerInfo {
    width: 100% !important;
    max-width: 100%;
    box-sizing: border-box;
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    line-height: 1.4;
}

.input-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.phone-input {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 8px;
}

/* 区域样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.85em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    justify-content: center;
    min-height: 36px;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #4facfe;
    border: 1px solid #4facfe;
}

.btn-outline:hover {
    background: #4facfe;
    color: white;
}

.btn-large {
    padding: 12px 24px;
    font-size: 0.9em;
    min-height: 44px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 输入区域 */
.action-section {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 特殊区域 */
.status-area {
    padding: 15px 20px;
    background: #fff;
}

.loading-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4facfe;
    font-size: 0.9em;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-left: 2px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-message {
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 0.85em;
    margin-top: 10px;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 状态指示器 */
.script-preview {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.script-preview h4 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.script-preview textarea {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    background: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
    resize: vertical;
}

.preview-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

/* 布局 */
.footer {
    padding: 15px;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 0.8em;
    color: #6c757d;
}

.footer p {
    margin-bottom: 5px;
}

.version {
    opacity: 0.7;
}

/* 按钮样式布局 */
@media (max-width: 350px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .input-actions {
        flex-direction: column;
    }
    
    .action-section {
        padding: 15px;
    }
    
    .phone-input {
        grid-template-columns: 1fr;
    }
}

/* 进度条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 废弃的图片上传样式已删除 */

.field-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.field-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    font-weight: 600;
    flex-grow: 1;
}

.lock-btn {
    background: none;
    border: 1px solid #ced4da;
    cursor: pointer;
    font-size: 12px;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.lock-btn.locked {
    background: var(--color-danger);
    color: #fff;
    border-color: var(--color-danger);
}

.lock-btn.unlocked {
    background: var(--color-success);
    color: #fff;
    border-color: var(--color-success);
}

.form-field[disabled],
.form-field[readonly] {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

 

#previewCode { width:100%; min-height:120px; background:var(--color-code-bg); color:#ecf0f1; border:1px solid #34495e; border-radius:4px; padding:6px; font-family:Consolas,monospace; font-size:11px; resize:vertical; }

/* ...existing code... */

/* 动画过渡 */

/* 动态组件样式增强 */
/* 移除废弃的文件项样式 */

/* 主题切换区域 */
.theme-toggle { background:rgba(255,255,255,.25); border:1px solid rgba(255,255,255,.5); color:#fff; padding:4px 8px; border-radius:14px; cursor:pointer; font-size:12px; }
.theme-toggle:hover { background:rgba(255,255,255,.4); }

/* 解析结果显示 */
.auto-parse-indicator { font-size:12px; color:var(--color-text-muted); }

/* 按钮和输入区域内容 */
.action-buttons { display:flex; gap:6px; flex-wrap:wrap; margin-top:8px; }

/* 差距、弹性 */
.diff-modal { position:fixed; inset:0; background:rgba(0,0,0,.45); display:flex; align-items:center; justify-content:center; z-index:999; }
.diff-modal.hidden { display:none; }
.diff-dialog { width:360px; max-height:70vh; overflow:auto; background:var(--color-surface); border:1px solid var(--color-border); border-radius:10px; padding:16px; box-shadow:0 6px 24px rgba(0,0,0,.25); }
.diff-dialog h3 { margin-top:0; font-size:16px; }
.diff-list { display:flex; flex-direction:column; gap:8px; margin:12px 0; }
.diff-item { border:1px solid var(--color-border); border-radius:6px; padding:8px; font-size:12px; background:var(--color-surface-alt); }
.diff-item strong { font-weight:600; }
.diff-from { color:#c92a2a; text-decoration:line-through; margin-right:6px; }
.diff-to { color:#2b8a3e; font-weight:600; }
.confidence-badge { background:var(--color-accent); color:#fff; padding:2px 6px; border-radius:10px; font-size:11px; margin-left:6px; }
.diff-actions { display:flex; gap:10px; justify-content:flex-end; }

/* Adaptive fill layout for sidepanel */
.container.layout-fill { height: 100vh; overflow: hidden; }

.container.layout-fill .main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(auto-fit, min-content);
  gap: 0; /* 完全消除间隙，实现贴合效果 */
  padding: 2px;
  overflow: auto;
}

/* Grid 布局定位规则 */
.container.layout-fill .main-content > .section-container { margin: 0; grid-column: 1; }

/* AI 输入区域 - 左列第1行 */
.container.layout-fill .main-content > .section-container:has(#travelerInfo) {
  grid-column: 1;
  grid-row: 1;
}

/* 游客信息表单 - 左列第2行 */
.container.layout-fill .main-content > #passengerForm {
  grid-column: 1;
  grid-row: 2;
}

/* 联系信息 - 右列第1行（顶部） */
.container.layout-fill .main-content > #contactInfo {
  grid-column: 2;
  grid-row: 1;
}

/* 行程信息表单 - 右列第2行 */
.container.layout-fill .main-content > #travelForm {
  grid-column: 2;
  grid-row: 2;
}

/* 状态区域 - 跨两列，在最底部 */
.container.layout-fill .main-content > #statusArea {
  grid-column: 1 / -1;
  grid-row: 3;
}

/* 脚本预览 - 跨两列，在状态区域上方 */
.container.layout-fill .main-content > #scriptPreview {
  grid-column: 1 / -1;
  grid-row: 4;
}



/* Keep status area visible at bottom inside the scroll container */
.container.layout-fill .main-content > #statusArea {
  position: sticky;
  bottom: 0;
  padding: 4px 6px;
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  z-index: 5;
}

/* Remove settings section (confidence/flags) visually */
.container.layout-fill .main-content > .section-container:has(#confidenceThreshold) {
  display: none !important;
}

/* Compact sizing for better density */
.container.layout-fill .btn { padding: 2px 6px; min-height: 24px; font-size: 11px; border-radius: 3px; }
.container.layout-fill .btn-large { padding: 4px 8px; min-height: 28px; font-size: 11px; }
.container.layout-fill .form-group { margin-bottom: 4px; }
.container.layout-fill .form-group label { font-size: 11px; margin-bottom: 2px; }
.container.layout-fill .form-group input,
.container.layout-fill .form-group select,
.container.layout-fill .form-group textarea { padding: 2px 4px; min-height: 22px; font-size: 10px; }
.container.layout-fill #travelerInfo {
    min-height: 60px;
    padding: 6px;
    width: 100% !important;
    max-width: 100%;
    box-sizing: border-box;
}

/* Horizontal action buttons */
.container.layout-fill .action-section { padding: 4px; background: transparent; border-top: none; display: flex; flex-direction: row; gap: 3px; flex-wrap: wrap; }

/* Narrow width fallback: single column */
/* 表单网格布局 - 支持响应式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 游客信息和行程信息单独表单的单列布局 */
#passengerForm .form-grid,
#travelForm .form-grid {
  grid-template-columns: 1fr;
}

/* 联系信息容器 - 使用 flex 布局 */
#contactInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

#contactInfo .form-grid {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: flex-start;
}

#contactInfo .form-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 联系信息中的生成脚本按钮样式 */
#contactInfo .btn-large {
  width: 100%;
  margin-top: 8px;
}

@media (max-width: 480px) {
  .form-grid {
    grid-template-columns: 1fr; /* 小屏幕单列显示 */
    gap: 6px;
  }

  /* 联系信息在小屏幕上改为垂直布局 */
  #contactInfo .form-grid {
    flex-direction: column;
    gap: 8px;
  }
  
  /* 小屏幕单列布局 */
  .container.layout-fill .main-content {
    grid-template-columns: 1fr;
  }
  
  .container.layout-fill .main-content > .section-container,
  .container.layout-fill .main-content > #contactInfo,
  .container.layout-fill .main-content > #passengerForm,
  .container.layout-fill .main-content > #travelForm,
  .container.layout-fill .main-content > #statusArea,
  .container.layout-fill .main-content > #scriptPreview {
    grid-column: 1;
  }
}

/* Upload button compact style */
.upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-surface);
  color: var(--color-text);
  cursor: pointer;
}
.upload-btn:hover { background: var(--color-surface-alt); }
.upload-btn .upload-icon { font-size: 14px; }


/* 图片上传区域已合并到 AI 区域 */

/* Place main form to top of right column */

/* Place contact info to bottom of left column */

/* AI 输入区域紧贴左上方 */

/* 图片上传模块已移除 */
