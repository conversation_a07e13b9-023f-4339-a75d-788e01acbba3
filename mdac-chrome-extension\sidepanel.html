<!DOCTYPE html>
<!--
  文件用途: MDAC Chrome 侧边栏页面
  依赖: sidepanel.css 样式, sidepanel.js 行为脚本, ./utils/* 功能模块
  技术栈: 原生 HTML/CSS/JS（Chrome 扩展 side panel）
  相关文件: sidepanel.css, sidepanel.js, utils/*
  核心功能:
    - 布局自适应两列，最大化填充侧边栏空间（见 sidepanel.css 中 .container.layout-fill）
    - 保留所有元素 ID，确保 JS 事件绑定不受影响
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC智能填充助手</title>
    <link rel="stylesheet" href="sidepanel.css">
    <link rel="stylesheet" href="unified/unified-styles.css">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; script-src 'self'; connect-src https://api.moonshot.cn https://api.z.ai https://ark.cn-beijing.volces.com">
    <meta name="color-scheme" content="light dark">
    <meta name="referrer" content="no-referrer">
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <meta name="x-ua-compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta name="application-name" content="MDAC Assistant">
    <meta name="theme-color" content="#4facfe">
</head>
<body>
    <div class="container layout-fill">
        <!-- 头部 -->
        <div class="header">
            <div class="header-content">
                <h1>MDAC</h1>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">就绪</span>
                    <button type="button" id="themeToggleBtn" class="theme-toggle" aria-label="切换主题">🌗</button>
                </div>
            </div>
        </div>

        <!-- 快速链接 -->
        <div class="quick-links">
            <a href="https://imigresen-online.imi.gov.my/mdac/main?registerMain" target="_blank" rel="noopener" class="mdac-link">🌐 打开MDAC官网</a>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- （高级设置：默认隐藏，保留ID供JS持久化） -->
            <div class="section-container" style="display:none">
                <div class="section-header">
                    <h3>⚙️ 高级设置</h3>
                </div>
                <div class="form-group">
                    <label for="confidenceThreshold">置信度阈值 (0-1)</label>
                    <input type="number" id="confidenceThreshold" min="0" max="1" step="0.05" value="0.55" class="form-field" />
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="enableMrzOcr" checked /> 启用本地MRZ解析（占位）</label>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="enableAddressEnhance" checked /> 启用地址智能增强</label>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="requireDiffConfirm" checked /> 应用前显示差异确认</label>
                </div>
            </div>

            <!-- 智能图片识别（容器隐藏，仅保留 input/预览节点以兼容逻辑） -->
            <div class="section-container" style="display:none">
                <div class="section-header">
                    <h3>📷 智能图片识别</h3>
                    <div class="beta-tag">Beta</div>
                </div>
                <div class="image-upload-section">
                    <input type="file" id="fileInput" multiple accept="image/png,image/jpeg" class="hidden" aria-hidden="true">
                    <div class="file-preview-grid hidden" id="filePreviewGrid"></div>
                    <button type="button" class="btn btn-secondary hidden" id="analyzeImagesBtn">🔍 分析图片</button>
                </div>
            </div>

            <!-- AI 输入区域 -->
            <div class="section-container">
                <div class="section-header">
                    <h3>🤖 AI 智能输入</h3>
                </div>
                <div class="input-group">
                    <textarea id="travelerInfo" rows="8" placeholder="输入旅客信息，AI将自动解析...

示例：
李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。
邮箱：<EMAIL>，手机：+60123456789
计划2025年8月1日到达马来西亚，8月7日离开
乘坐MH123航班，住宿地址：Hotel KL City Center，邮编50000"></textarea>
                    <div class="input-actions">
                        <button id="uploadDropzone" class="btn btn-outline" type="button">上传文件</button>
                        <button id="aiParseBtn" class="btn btn-primary" type="button">AI解析</button>
                        <span id="autoParseIndicator" class="auto-parse-indicator" aria-live="polite" hidden>⏳ 自动解析...</span>
                        <button id="fillSampleBtn" class="btn btn-secondary" type="button">填入示例</button>
                        <button id="clearBtn" class="btn btn-outline" type="button">🗑️ 清空</button>
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="section-container" id="contactInfo">
                <div class="section-header">
                    <h3>📞 联系信息（自动保存）</h3>
                </div>
                <div class="form-grid">
                    <div class="form-column">
                        <div class="form-group">
                            <div class="field-label">
                                <label for="email">电子邮箱 (Email)</label>
                                <button class="lock-btn unlocked" data-field="email">🔓</button>
                            </div>
                            <input type="email" id="email" placeholder="<EMAIL>" class="form-field" autocomplete="email">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="confirmEmail">确认邮箱 (Confirm Email)</label>
                                <button class="lock-btn unlocked" data-field="confirmEmail">🔓</button>
                            </div>
                            <input type="email" id="confirmEmail" placeholder="<EMAIL>" class="form-field" autocomplete="email">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="phoneNumber">手机号 (Mobile)</label>
                                <button class="lock-btn unlocked" data-field="phoneNumber">🔓</button>
                            </div>
                            <div class="phone-input">
                                <select id="phoneRegion" class="form-field" aria-label="电话区号">
                                    <option value="60" selected>+60 (马来西亚)</option>
                                    <option value="86">+86 (中国)</option>
                                    <option value="1">+1 (美国/加拿大)</option>
                                    <option value="65">+65 (新加坡)</option>
                                    <option value="44">+44 (英国)</option>
                                    <option value="81">+81 (日本)</option>
                                    <option value="82">+82 (韩国)</option>
                                    <option value="66">+66 (泰国)</option>
                                    <option value="61">+61 (澳大利亚)</option>
                                    <option value="64">+64 (新西兰)</option>
                                    <option value="33">+33 (法国)</option>
                                    <option value="49">+49 (德国)</option>
                                    <option value="39">+39 (意大利)</option>
                                    <option value="34">+34 (西班牙)</option>
                                    <option value="31">+31 (荷兰)</option>
                                    <option value="41">+41 (瑞士)</option>
                                    <option value="46">+46 (瑞典)</option>
                                    <option value="47">+47 (挪威)</option>
                                    <option value="45">+45 (丹麦)</option>
                                    <option value="358">+358 (芬兰)</option>
                                    <option value="32">+32 (比利时)</option>
                                    <option value="43">+43 (奥地利)</option>
                                    <option value="353">+353 (爱尔兰)</option>
                                    <option value="351">+351 (葡萄牙)</option>
                                    <option value="30">+30 (希腊)</option>
                                    <option value="48">+48 (波兰)</option>
                                    <option value="420">+420 (捷克)</option>
                                    <option value="36">+36 (匈牙利)</option>
                                    <option value="7">+7 (俄罗斯)</option>
                                    <option value="91">+91 (印度)</option>
                                    <option value="62">+62 (印尼)</option>
                                    <option value="63">+63 (菲律宾)</option>
                                    <option value="84">+84 (越南)</option>
                                    <option value="856">+856 (老挝)</option>
                                    <option value="855">+855 (柬埔寨)</option>
                                    <option value="95">+95 (缅甸)</option>
                                    <option value="673">+673 (文莱)</option>
                                    <option value="886">+886 (台湾)</option>
                                    <option value="852">+852 (香港)</option>
                                    <option value="853">+853 (澳门)</option>
                                    <option value="27">+27 (南非)</option>
                                    <option value="20">+20 (埃及)</option>
                                    <option value="971">+971 (阿联酋)</option>
                                    <option value="966">+966 (沙特阿拉伯)</option>
                                    <option value="974">+974 (卡塔尔)</option>
                                    <option value="965">+965 (科威特)</option>
                                    <option value="973">+973 (巴林)</option>
                                    <option value="968">+968 (阿曼)</option>
                                    <option value="962">+962 (约旦)</option>
                                    <option value="961">+961 (黎巴嫩)</option>
                                    <option value="972">+972 (以色列)</option>
                                    <option value="90">+90 (土耳其)</option>
                                    <option value="98">+98 (伊朗)</option>
                                    <option value="964">+964 (伊拉克)</option>
                                    <option value="92">+92 (巴基斯坦)</option>
                                    <option value="880">+880 (孟加拉国)</option>
                                    <option value="94">+94 (斯里兰卡)</option>
                                    <option value="977">+977 (尼泊尔)</option>
                                    <option value="975">+975 (不丹)</option>
                                    <option value="960">+960 (马尔代夫)</option>
                                    <option value="93">+93 (阿富汗)</option>
                                    <option value="7">+7 (哈萨克斯坦)</option>
                                    <option value="998">+998 (乌兹别克斯坦)</option>
                                    <option value="996">+996 (吉尔吉斯斯坦)</option>
                                    <option value="992">+992 (塔吉克斯坦)</option>
                                    <option value="993">+993 (土库曼斯坦)</option>
                                    <option value="976">+976 (蒙古)</option>
                                    <option value="850">+850 (朝鲜)</option>
                                    <option value="55">+55 (巴西)</option>
                                    <option value="54">+54 (阿根廷)</option>
                                    <option value="56">+56 (智利)</option>
                                    <option value="51">+51 (秘鲁)</option>
                                    <option value="57">+57 (哥伦比亚)</option>
                                    <option value="58">+58 (委内瑞拉)</option>
                                    <option value="593">+593 (厄瓜多尔)</option>
                                    <option value="591">+591 (玻利维亚)</option>
                                    <option value="595">+595 (巴拉圭)</option>
                                    <option value="598">+598 (乌拉圭)</option>
                                    <option value="592">+592 (圭亚那)</option>
                                    <option value="597">+597 (苏里南)</option>
                                    <option value="52">+52 (墨西哥)</option>
                                    <option value="502">+502 (危地马拉)</option>
                                    <option value="501">+501 (伯利兹)</option>
                                    <option value="503">+503 (萨尔瓦多)</option>
                                    <option value="504">+504 (洪都拉斯)</option>
                                    <option value="505">+505 (尼加拉瓜)</option>
                                    <option value="506">+506 (哥斯达黎加)</option>
                                    <option value="507">+507 (巴拿马)</option>
                                    <option value="53">+53 (古巴)</option>
                                    <option value="1876">+1876 (牙买加)</option>
                                    <option value="509">+509 (海地)</option>
                                    <option value="1809">+1809 (多米尼加)</option>
                                    <option value="1868">+1868 (特立尼达和多巴哥)</option>
                                    <option value="1246">+1246 (巴巴多斯)</option>
                                    <option value="234">+234 (尼日利亚)</option>
                                    <option value="254">+254 (肯尼亚)</option>
                                    <option value="251">+251 (埃塞俄比亚)</option>
                                    <option value="233">+233 (加纳)</option>
                                    <option value="256">+256 (乌干达)</option>
                                    <option value="255">+255 (坦桑尼亚)</option>
                                    <option value="258">+258 (莫桑比克)</option>
                                    <option value="263">+263 (津巴布韦)</option>
                                    <option value="260">+260 (赞比亚)</option>
                                    <option value="265">+265 (马拉维)</option>
                                    <option value="267">+267 (博茨瓦纳)</option>
                                    <option value="264">+264 (纳米比亚)</option>
                                    <option value="266">+266 (莱索托)</option>
                                    <option value="268">+268 (斯威士兰)</option>
                                    <option value="261">+261 (马达加斯加)</option>
                                    <option value="230">+230 (毛里求斯)</option>
                                    <option value="248">+248 (塞舌尔)</option>
                                    <option value="269">+269 (科摩罗)</option>
                                </select>
                                <input type="text" id="phoneNumber" placeholder="123456789" class="form-field" inputmode="numeric" autocomplete="tel">
                            </div>
                        </div>
                        <div class="form-group">
                            <button id="generateScriptBtn" class="btn btn-success btn-large">🚀 生成并执行脚本</button>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 游客信息表单 -->
            <div class="section-container" id="passengerForm">
                <div class="section-header">
                    <h3>� 游客信息</h3>
                    <div class="field-controls">
                        <button id="lockAllPassengerBtn" class="lock-btn unlocked">
                            <span class="lock-icon">🔓</span>
                            <span class="lock-text">全部锁定</span>
                        </button>
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-column">
                        <div class="form-group">
                            <div class="field-label">
                                <label for="passengerName">姓名 (Name)</label>
                                <button class="lock-btn unlocked" data-field="passengerName">🔓</button>
                            </div>
                            <input type="text" id="passengerName" placeholder="LI MING" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="passportNo">护照号码 (Passport No.)</label>
                                <button class="lock-btn unlocked" data-field="passportNo">🔓</button>
                            </div>
                            <input type="text" id="passportNo" placeholder="G12345678" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="birthDate">出生日期 (Date of Birth)</label>
                                <button class="lock-btn unlocked" data-field="birthDate">🔓</button>
                            </div>
                            <input type="date" id="birthDate" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="nationality">国籍 (Nationality)</label>
                                <button class="lock-btn unlocked" data-field="nationality">🔓</button>
                            </div>
                            <select id="nationality" class="form-field">
                                <option value="">请选择国籍</option>
                                <option value="CHN">中国 (CHN)</option>
                                <option value="USA">美国 (USA)</option>
                                <option value="GBR">英国 (GBR)</option>
                                <option value="SGP">新加坡 (SGP)</option>
                                <option value="JPN">日本 (JPN)</option>
                                <option value="KOR">韩国 (KOR)</option>
                                <option value="THA">泰国 (THA)</option>
                                <option value="AUS">澳大利亚 (AUS)</option>
                                <option value="CAN">加拿大 (CAN)</option>
                                <option value="FRA">法国 (FRA)</option>
                                <option value="DEU">德国 (DEU)</option>
                                <option value="ITA">意大利 (ITA)</option>
                                <option value="ESP">西班牙 (ESP)</option>
                                <option value="NLD">荷兰 (NLD)</option>
                                <option value="CHE">瑞士 (CHE)</option>
                                <option value="SWE">瑞典 (SWE)</option>
                                <option value="NOR">挪威 (NOR)</option>
                                <option value="DNK">丹麦 (DNK)</option>
                                <option value="FIN">芬兰 (FIN)</option>
                                <option value="BEL">比利时 (BEL)</option>
                                <option value="AUT">奥地利 (AUT)</option>
                                <option value="IRL">爱尔兰 (IRL)</option>
                                <option value="PRT">葡萄牙 (PRT)</option>
                                <option value="GRC">希腊 (GRC)</option>
                                <option value="POL">波兰 (POL)</option>
                                <option value="CZE">捷克 (CZE)</option>
                                <option value="HUN">匈牙利 (HUN)</option>
                                <option value="RUS">俄罗斯 (RUS)</option>
                                <option value="IND">印度 (IND)</option>
                                <option value="IDN">印尼 (IDN)</option>
                                <option value="MYS">马来西亚 (MYS)</option>
                                <option value="PHL">菲律宾 (PHL)</option>
                                <option value="VNM">越南 (VNM)</option>
                                <option value="LAO">老挝 (LAO)</option>
                                <option value="KHM">柬埔寨 (KHM)</option>
                                <option value="MMR">缅甸 (MMR)</option>
                                <option value="BRN">文莱 (BRN)</option>
                                <option value="TWN">台湾 (TWN)</option>
                                <option value="HKG">香港 (HKG)</option>
                                <option value="MAC">澳门 (MAC)</option>
                                <option value="NZL">新西兰 (NZL)</option>
                                <option value="ZAF">南非 (ZAF)</option>
                                <option value="EGY">埃及 (EGY)</option>
                                <option value="ARE">阿联酋 (ARE)</option>
                                <option value="SAU">沙特阿拉伯 (SAU)</option>
                                <option value="QAT">卡塔尔 (QAT)</option>
                                <option value="KWT">科威特 (KWT)</option>
                                <option value="BHR">巴林 (BHR)</option>
                                <option value="OMN">阿曼 (OMN)</option>
                                <option value="JOR">约旦 (JOR)</option>
                                <option value="LBN">黎巴嫩 (LBN)</option>
                                <option value="ISR">以色列 (ISR)</option>
                                <option value="TUR">土耳其 (TUR)</option>
                                <option value="IRN">伊朗 (IRN)</option>
                                <option value="IRQ">伊拉克 (IRQ)</option>
                                <option value="PAK">巴基斯坦 (PAK)</option>
                                <option value="BGD">孟加拉国 (BGD)</option>
                                <option value="LKA">斯里兰卡 (LKA)</option>
                                <option value="NPL">尼泊尔 (NPL)</option>
                                <option value="BTN">不丹 (BTN)</option>
                                <option value="MDV">马尔代夫 (MDV)</option>
                                <option value="AFG">阿富汗 (AFG)</option>
                                <option value="KAZ">哈萨克斯坦 (KAZ)</option>
                                <option value="UZB">乌兹别克斯坦 (UZB)</option>
                                <option value="KGZ">吉尔吉斯斯坦 (KGZ)</option>
                                <option value="TJK">塔吉克斯坦 (TJK)</option>
                                <option value="TKM">土库曼斯坦 (TKM)</option>
                                <option value="MNG">蒙古 (MNG)</option>
                                <option value="PRK">朝鲜 (PRK)</option>
                                <option value="BRA">巴西 (BRA)</option>
                                <option value="ARG">阿根廷 (ARG)</option>
                                <option value="CHL">智利 (CHL)</option>
                                <option value="PER">秘鲁 (PER)</option>
                                <option value="COL">哥伦比亚 (COL)</option>
                                <option value="VEN">委内瑞拉 (VEN)</option>
                                <option value="ECU">厄瓜多尔 (ECU)</option>
                                <option value="BOL">玻利维亚 (BOL)</option>
                                <option value="PRY">巴拉圭 (PRY)</option>
                                <option value="URY">乌拉圭 (URY)</option>
                                <option value="GUY">圭亚那 (GUY)</option>
                                <option value="SUR">苏里南 (SUR)</option>
                                <option value="MEX">墨西哥 (MEX)</option>
                                <option value="GTM">危地马拉 (GTM)</option>
                                <option value="BLZ">伯利兹 (BLZ)</option>
                                <option value="SLV">萨尔瓦多 (SLV)</option>
                                <option value="HND">洪都拉斯 (HND)</option>
                                <option value="NIC">尼加拉瓜 (NIC)</option>
                                <option value="CRI">哥斯达黎加 (CRI)</option>
                                <option value="PAN">巴拿马 (PAN)</option>
                                <option value="CUB">古巴 (CUB)</option>
                                <option value="JAM">牙买加 (JAM)</option>
                                <option value="HTI">海地 (HTI)</option>
                                <option value="DOM">多米尼加 (DOM)</option>
                                <option value="TTO">特立尼达和多巴哥 (TTO)</option>
                                <option value="BRB">巴巴多斯 (BRB)</option>
                                <option value="NGA">尼日利亚 (NGA)</option>
                                <option value="KEN">肯尼亚 (KEN)</option>
                                <option value="ETH">埃塞俄比亚 (ETH)</option>
                                <option value="GHA">加纳 (GHA)</option>
                                <option value="UGA">乌干达 (UGA)</option>
                                <option value="TZA">坦桑尼亚 (TZA)</option>
                                <option value="MOZ">莫桑比克 (MOZ)</option>
                                <option value="ZWE">津巴布韦 (ZWE)</option>
                                <option value="ZMB">赞比亚 (ZMB)</option>
                                <option value="MWI">马拉维 (MWI)</option>
                                <option value="BWA">博茨瓦纳 (BWA)</option>
                                <option value="NAM">纳米比亚 (NAM)</option>
                                <option value="LSO">莱索托 (LSO)</option>
                                <option value="SWZ">斯威士兰 (SWZ)</option>
                                <option value="MDG">马达加斯加 (MDG)</option>
                                <option value="MUS">毛里求斯 (MUS)</option>
                                <option value="SYC">塞舌尔 (SYC)</option>
                                <option value="COM">科摩罗 (COM)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="gender">性别 (Gender)</label>
                                <button class="lock-btn unlocked" data-field="gender">🔓</button>
                            </div>
                            <select id="gender" class="form-field">
                                <option value="">请选择</option>
                                <option value="1">男性 (Male)</option>
                                <option value="2">女性 (Female)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="passportExpiry">护照有效期 (Passport Expiry)</label>
                                <button class="lock-btn unlocked" data-field="passportExpiry">🔓</button>
                            </div>
                            <input type="date" id="passportExpiry" class="form-field">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行程信息表单 -->
            <div class="section-container" id="travelForm">
                <div class="section-header">
                    <h3>✈️ 行程信息</h3>
                    <div class="field-controls">
                        <button id="lockAllTravelBtn" class="lock-btn unlocked">
                            <span class="lock-icon">🔓</span>
                            <span class="lock-text">全部锁定</span>
                        </button>
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-column">
                        <div class="form-group">
                            <div class="field-label">
                                <label for="arrivalDate">到达日期 (Arrival Date)</label>
                                <button class="lock-btn unlocked" data-field="arrivalDate">🔓</button>
                            </div>
                            <input type="date" id="arrivalDate" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="departureDate">离开日期 (Departure Date)</label>
                                <button class="lock-btn unlocked" data-field="departureDate">🔓</button>
                            </div>
                            <input type="date" id="departureDate" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="flightNo">航班号 (Flight No.)</label>
                                <button class="lock-btn unlocked" data-field="flightNo">🔓</button>
                            </div>
                            <input type="text" id="flightNo" placeholder="MH123" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="embark">出发国家/港口 (Embark)</label>
                                <button class="lock-btn unlocked" data-field="embark">🔓</button>
                            </div>
                            <select id="embark" class="form-field">
                                <option value="">请选择出发地</option>
                                <option value="CHN">中国 (CHN)</option>
                                <option value="SGP">新加坡 (SGP)</option>
                                <option value="THA">泰国 (THA)</option>
                                <option value="IDN">印尼 (IDN)</option>
                                <option value="VNM">越南 (VNM)</option>
                                <option value="PHL">菲律宾 (PHL)</option>
                                <option value="JPN">日本 (JPN)</option>
                                <option value="KOR">韩国 (KOR)</option>
                                <option value="AUS">澳大利亚 (AUS)</option>
                                <option value="IND">印度 (IND)</option>
                                <option value="USA">美国 (USA)</option>
                                <option value="GBR">英国 (GBR)</option>
                                <option value="other">其他 (Other)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="travelMode">交通方式 (Mode of Travel)</label>
                                <button class="lock-btn unlocked" data-field="travelMode">🔓</button>
                            </div>
                            <select id="travelMode" class="form-field">
                                <option value="1">飞机 (Air)</option>
                                <option value="2">陆路 (Land)</option>
                                <option value="3">海运 (Sea)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="accommodationType">住宿类型 (Accommodation)</label>
                                <button class="lock-btn unlocked" data-field="accommodationType">🔓</button>
                            </div>
                            <select id="accommodationType" class="form-field">
                                <option value="01">酒店 (Hotel)</option>
                                <option value="02">亲友住所 (Relative's Place)</option>
                                <option value="99">其他 (Others)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="address1">住宿地址 (Address)</label>
                                <button class="lock-btn unlocked" data-field="address1">🔓</button>
                            </div>
                            <input type="text" id="address1" placeholder="Hotel KL City Center" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="address2">地址第二行 (Address Line 2)</label>
                                <button class="lock-btn unlocked" data-field="address2">🔓</button>
                            </div>
                            <input type="text" id="address2" placeholder="Floor 10, Room 1001" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="city">城市 (City)</label>
                                <button class="lock-btn unlocked" data-field="city">🔓</button>
                            </div>
                            <input type="text" id="city" placeholder="KUALA LUMPUR" class="form-field">
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="state">州属 (State)</label>
                                <button class="lock-btn unlocked" data-field="state">🔓</button>
                            </div>
                            <select id="state" class="form-field">
                                <option value="">请选择州属</option>
                                <option value="14">吉隆坡 (KL)</option>
                                <option value="10">雪兰莪 (Selangor)</option>
                                <option value="01">柔佛 (Johor)</option>
                                <option value="07">槟城 (Penang)</option>
                                <option value="16">布城 (WP Putrajaya)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="field-label">
                                <label for="postcode">邮编 (Postcode)</label>
                                <button class="lock-btn unlocked" data-field="postcode">🔓</button>
                            </div>
                            <input type="text" id="postcode" placeholder="50000" class="form-field">
                        </div>
                    </div>
                </div>
            </div>


            <!-- 状态显示 -->
            <div id="statusArea" class="status-area" aria-live="polite" role="status">
                <div id="loadingIndicator" class="loading-indicator hidden">
                    <div class="spinner"></div>
                    <span>AI解析中...</span>
                </div>
                <div id="statusMessage" class="status-message"></div>
            </div>

            <!-- 脚本预览 -->
            <div id="scriptPreview" class="section-container hidden">
                <div class="section-header">
                    <h3>📜 脚本预览</h3>
                </div>
                <textarea id="previewCode" readonly aria-label="生成的脚本代码"></textarea>
                <div class="action-buttons">
                    <button id="copyScriptBtn" class="btn btn-secondary" type="button">📋 复制</button>
                    <button id="beautifyScriptBtn" class="btn btn-outline" type="button" title="格式化脚本">🎨 格式化</button>
                    <button id="minifyScriptBtn" class="btn btn-outline" type="button" title="压缩脚本">📦 压缩</button>
                    <button id="resetScriptBtn" class="btn btn-outline" type="button" title="重新生成脚本">🔄 重置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 差异确认弹层 -->
    <div id="diffModal" class="diff-modal hidden" role="dialog" aria-modal="true" aria-labelledby="diffModalTitle">
        <div class="diff-dialog">
            <h3 id="diffModalTitle">🔍 字段变更确认</h3>
            <div id="diffList" class="diff-list"></div>
            <div class="diff-actions">
                <button id="applyDiffBtn" class="btn btn-success" type="button">✅ 应用更新</button>
                <button id="cancelDiffBtn" class="btn btn-outline" type="button">取消</button>
            </div>
        </div>
    </div>
    <div id="loadingOverlay" class="hidden">
        <div class="spinner"></div>
    </div>
    <!-- 统一多模态系统 -->
    <script src="./unified/ImageProcessor.js"></script>
    <script src="./unified/FileProcessor.js"></script>
    <script src="./unified/UnifiedMultiModalProcessor.js"></script>
    <script src="./unified/UIController.js"></script>
    <script src="./unified/field-mapping-validator.js"></script>
    <script src="./unified/integration-patch.js"></script>

    <!-- 原有工具模块 -->
    <script src="./utils/form-mapper.js"></script>
    <script src="./utils/data-validator.js"></script>
    <script src="./utils/address-enhancer.js"></script>
    <script src="./utils/SmartPreprocessor.js"></script>
    <script src="./utils/performance-analytics.js"></script>
    <script src="sidepanel.js"></script>
</body>
</html>
