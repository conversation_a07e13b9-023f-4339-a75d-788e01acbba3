// MDAC Chrome扩展 - 侧边栏主要功能类
// 用途：提供MDAC表单智能填充功能的Chrome扩展侧边栏界面
// 依赖：Chrome Extension APIs, Doubao LLM API, UnifiedMultiModalProcessor
// 技术栈：原生JavaScript + Chrome Extension Manifest V3
// 核心功能：文本解析、图片识别、表单填充、数据持久化

class MDACExtension {
    constructor() {
        // 数据状态管理
        this.currentData = {};
        this.isConnected = false;
        this.tabInfo = null;

        // 字段锁定功能
        this.lockedFields = new Set();
        this.lockStorageKey = 'mdacFieldLocks';

        // 文件上传管理
        this.selectedFiles = [];

        // 自动解析功能
        this.autoParseTimer = null;
        this.autoParseDelay = 800; // ms
        this.lastParsedTextHash = '';

        // 配置管理
        this.themeKey = 'mdacTheme';
        this.contactKey = 'mdacContact';
        this.visionConfigKey = 'mdacVisionConfig';
        this.visionConfig = {
            confidenceThreshold: 0.55,
            enableMrzOcr: true,
            enableAddressEnhance: true,
            requireDiffConfirm: true
        };

        // 待处理数据
        this.pendingMergedData = null;
        this.pendingDiff = null;

        // 性能监控
        this.cacheStatsTimer = null;

        // 启动初始化
        this.init();
    }

    async init() {
        console.log('初始化 MDAC扩展侧边栏组件...');

        try {
            // 绑定事件监听器
            this.bindEvents();

            // 检查与MDAC网站的连接状态
            await this.checkConnection();

            // 加载用户保存的数据
            await this.loadSavedData();

            // 加载字段锁定状态
            await this.loadFieldLocks();
            this.applyLockedStateToUI();

            // 加载主题配置
            this.loadTheme();

            // 加载联系信息快捷缓存
            await this.loadContactInfo();

            // 加载视觉模型配置
            await this.loadVisionConfig();

            console.log('✅ 侧边栏组件初始化完成');

            // 启动缓存统计定时器
            this.startCacheStatsLogging();

        } catch (error) {
            console.error('❌ MDAC扩展组件初始化失败:', error);
            console.error('错误堆栈:', error.stack);

            // 显示错误状态给用户
            this.showStatus(`初始化失败: ${error.message}`, 'error');

            // 仍然尝试继续运行，但某些功能可能不可用
            console.warn('⚠️ 尝试以降级模式继续运行...');
        }
    }

    bindEvents() {
        try {
            // 自动解析功能 - 监听旅客信息输入框
            const travelerInfoEl = document.getElementById('travelerInfo');
            if (travelerInfoEl) {
                travelerInfoEl.addEventListener('input', () => this.handleAutoParseInput());
                console.log('✅ 自动解析事件已绑定到 travelerInfo');
            } else {
                console.error('❌ 找不到 travelerInfo 元素，自动解析功能将不工作');
            }

            // 主要操作按钮事件绑定
            const generateBtn = document.getElementById('generateScriptBtn');
            const fillBtn = document.getElementById('fillSampleBtn');
            const parseBtn = document.getElementById('aiParseBtn');
            const clearBtn = document.getElementById('clearBtn');
            const analyzeBtn = document.getElementById('analyzeImagesBtn');
            const copyBtn = document.getElementById('copyScriptBtn');
            const beautifyBtn = document.getElementById('beautifyScriptBtn');
            const minifyBtn = document.getElementById('minifyScriptBtn');
            const resetBtn = document.getElementById('resetScriptBtn');

            if (generateBtn) generateBtn.addEventListener('click', () => this.generateAndFillForm());
            if (fillBtn) fillBtn.addEventListener('click', () => this.fillFormOnly());
            if (parseBtn) parseBtn.addEventListener('click', () => this.handleAIParse());
            if (clearBtn) clearBtn.addEventListener('click', () => this.clearAll());
            if (analyzeBtn) analyzeBtn.addEventListener('click', () => this.extractDataFromImages(this.selectedFiles));
            if (copyBtn) copyBtn.addEventListener('click', () => this.copyScript());
            if (beautifyBtn) beautifyBtn.addEventListener('click', () => this.beautifyScript());
            if (minifyBtn) minifyBtn.addEventListener('click', () => this.minifyScript());
            if (resetBtn) resetBtn.addEventListener('click', () => this.resetScript());

            // 图片上传相关事件
            this.initImageUploadEvents();

            // 字段锁定相关事件
            this.initFieldLockEvents();
            // Robust binding for lock buttons (data-field) and group locks
            if (typeof this.initFieldLockEventsV2 !== 'function') {
                this.initFieldLockEventsV2 = () => {
                    const lockButtons = document.querySelectorAll('.lock-btn[data-field]');
                    lockButtons.forEach(btn => {
                        const fieldId = btn.getAttribute('data-field');
                        if (!fieldId) return;
                        if (!btn.__mdacBound) {
                            btn.addEventListener('click', () => this.toggleFieldLock(fieldId, btn));
                            btn.__mdacBound = true;
                        }
                    });
                    const passengerGroup = ['passengerName','passportNo','birthDate','nationality','gender','passportExpiry'];
                    const travelGroup = ['arrivalDate','departureDate','flightNo','travelMode','accommodationType','address1','state','postcode'];
                    const pBtn = document.getElementById('lockAllPassengerBtn');
                    if (pBtn && !pBtn.__mdacBound) {
                        pBtn.addEventListener('click', () => {
                            const shouldLock = !passengerGroup.every(id => this.lockedFields.has(id));
                            passengerGroup.forEach(id => this.setFieldLock(id, shouldLock));
                            this.updateGroupLockButton(pBtn, shouldLock);
                        });
                        pBtn.__mdacBound = true;
                    }
                    const tBtn = document.getElementById('lockAllTravelBtn');
                    if (tBtn && !tBtn.__mdacBound) {
                        tBtn.addEventListener('click', () => {
                            const shouldLock = !travelGroup.every(id => this.lockedFields.has(id));
                            travelGroup.forEach(id => this.setFieldLock(id, shouldLock));
                            this.updateGroupLockButton(tBtn, shouldLock);
                        });
                        tBtn.__mdacBound = true;
                    }
                };
            }
            this.initFieldLockEventsV2();

            // 主题切换事件
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) themeBtn.addEventListener('click', () => this.toggleTheme());

            // 高级设置事件
            this.bindAdvancedSettings();

            console.log('✅ 事件绑定完成');

        } catch (error) {
            console.error('❌ 事件绑定失败:', error);
        }
    }

    // 统一入口：若选择了文件则走多模态处理，否则走文本解析
    async handleAIParse() {
        try {
            // 若UI控制器可用且检测到已选择文件，走多模态统一流程
            if (window.uiController && typeof window.uiController.integrateWithExistingAIParse === 'function') {
                const used = await window.uiController.integrateWithExistingAIParse();
                if (used) return;
            }
            // 默认回退到文本/本地图片解析
            this.parseContent();
        } catch (err) {
            console.error('handleAIParse error:', err);
            this.parseContent();
        }
    }

    // 供 unified/integration-patch.js 调用，将AI结果映射到侧边栏表单并生成注入脚本
    applyFieldUpdates(data) {
        if (!data || typeof data !== 'object') return;
        try {
            this.currentData = { ...this.currentData, ...data };
            this.updateFormFields();
            this.saveData?.();
            this.generateScript();
            this.showStatus('已应用AI解析结果并生成脚本', 'success');
        } catch (e) {
            console.error('applyFieldUpdates error:', e);
            this.showStatus('应用AI结果失败', 'error');
        }
    }

    initImageUploadEvents() {
        const imageUpload = document.getElementById('imageUpload');
        const dropZone = document.getElementById('dropZone');

        if (imageUpload) {
            imageUpload.addEventListener('change', (e) => this.handleImageSelect(e));
        }

        if (dropZone) {
            // 拖拽上传功能
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                this.handleImageDrop(e);
            });

            // 点击上传功能
            dropZone.addEventListener('click', () => {
                if (imageUpload) imageUpload.click();
            });
        }
    }

    initFieldLockEvents() {
        // 为每个表单字段添加锁定/解锁功能
        const fieldIds = [
            'passengerName', 'passportNo', 'birthDate', 'passportExpiry',
            'email', 'mobile', 'nationality', 'gender', 'arrivalDate',
            'departureDate', 'flightNo', 'accommodationType', 'address1',
            'state', 'postcode'
        ];

        fieldIds.forEach(fieldId => {
            const lockBtn = document.getElementById(`${fieldId}Lock`);
            if (lockBtn) {
                lockBtn.addEventListener('click', () => this.toggleFieldLock(fieldId));
            }
        });
    }

    bindAdvancedSettings() {
        // 置信度阈值设置
        const confidenceSlider = document.getElementById('confidenceThreshold');
        if (confidenceSlider) {
            confidenceSlider.addEventListener('input', (e) => {
                this.visionConfig.confidenceThreshold = parseFloat(e.target.value);
                this.saveVisionConfig();
            });
        }

        // OCR设置
        const mrzCheckbox = document.getElementById('enableMrzOcr');
        const addressCheckbox = document.getElementById('enableAddressEnhance');
        const diffCheckbox = document.getElementById('requireDiffConfirm');

        if (mrzCheckbox) {
            mrzCheckbox.addEventListener('change', (e) => {
                this.visionConfig.enableMrzOcr = e.target.checked;
                this.saveVisionConfig();
            });
        }

        if (addressCheckbox) {
            addressCheckbox.addEventListener('change', (e) => {
                this.visionConfig.enableAddressEnhance = e.target.checked;
                this.saveVisionConfig();
            });
        }

        if (diffCheckbox) {
            diffCheckbox.addEventListener('change', (e) => {
                this.visionConfig.requireDiffConfirm = e.target.checked;
                this.saveVisionConfig();
            });
        }
    }

    async handleAutoParseInput() {
        const travelerInfo = document.getElementById('travelerInfo');
        if (!travelerInfo) return;

        const currentText = travelerInfo.value.trim();
        const currentHash = this.hashString(currentText);

        // 避免重复解析相同内容
        if (currentHash === this.lastParsedTextHash || currentText.length < 10) {
            return;
        }

        // 清除之前的定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
        }

        // 设置新的定时器 - 防抖处理
        this.autoParseTimer = setTimeout(async () => {
            try {
                this.lastParsedTextHash = currentHash;
                console.log('🚀 自动解析触发...');

                await this.parseTextContent(currentText);

            } catch (error) {
                console.error('❌ 自动解析失败:', error);
            }
        }, this.autoParseDelay);
    }

    async parseTextContent(text) {
        if (!text || text.trim().length < 10) {
            this.showStatus('输入内容太短，无法解析', 'warning');
            return;
        }

        this.showStatus('正在解析文本内容...', 'loading');

        try {
            // 调用豆包API进行文本解析
            const result = await this.callDoubaoAPI(text);

            if (result && result.data) {
                // 合并解析结果到当前数据
                this.currentData = { ...this.currentData, ...result.data };

                // 更新界面显示
                this.updateFormFields();
                this.saveData(); // 自动保存

                this.showStatus(`✅ 文本解析完成，提取到 ${Object.keys(result.data).length} 个字段`, 'success');

                return result.data;
            } else {
                this.showStatus('❌ 解析失败：无法提取有效数据', 'error');
                return null;
            }

        } catch (error) {
            console.error('❌ 文本解析错误:', error);
            this.showStatus(`❌ 解析错误: ${error.message}`, 'error');
            throw error;
        }
    }

    async extractDataFromImages(files) {
        if (!files || files.length === 0) {
            throw new Error('没有选择图片文件');
        }

        this.showStatus(`正在处理 ${files.length} 张图片...`, 'loading');

        try {
            // 使用统一多模态处理器处理图片
            const processor = window.UnifiedMultiModalProcessor?.getInstance();
            if (!processor) {
                throw new Error('多模态处理器未加载，请检查依赖文件');
            }

            const result = await processor.processImages(files, this.visionConfig);

            if (result && result.data) {
                // 合并图片解析结果
                this.currentData = { ...this.currentData, ...result.data };

                // 更新界面
                this.updateFormFields();
                this.saveData(); // 自动保存

                this.showStatus(`✅ 图片处理完成，提取到 ${Object.keys(result.data).length} 个字段`, 'success');

                return result.data;
            } else {
                throw new Error('图片处理返回无效数据');
            }

        } catch (error) {
            console.error('❌ 图片处理错误:', error);
            this.showStatus(`❌ 图片处理错误: ${error.message}`, 'error');
            throw error;
        }
    }

    async generateAndFillForm() {
        try {
            this.showStatus('正在生成表单数据...', 'loading');

            let allData = { ...this.currentData };
            let hasNewData = false;

            // 处理图片数据
            if (this.selectedFiles.length > 0) {
                console.log(`处理 ${this.selectedFiles.length} 张图片...`);
                const imageData = await this.extractDataFromImages(this.selectedFiles);
                if (imageData) {
                    allData = { ...allData, ...imageData };
                    hasNewData = true;
                }
            }

            // 处理文本数据
            const travelerInfo = document.getElementById('travelerInfo')?.value?.trim();
            if (travelerInfo && travelerInfo.length > 10) {
                console.log('处理文本输入...');
                const textData = await this.parseTextContent(travelerInfo);
                if (textData) {
                    allData = { ...allData, ...textData };
                    hasNewData = true;
                }
            }

            // 检查是否有数据可以填充
            if (Object.keys(allData).length === 0) {
                this.showStatus('❌ 没有可用数据进行填充，请先输入文本或上传图片', 'error');
                return;
            }

            // 更新数据并生成脚本
            this.currentData = allData;
            this.generateScript();

            // 重新检查连接并自动填充表单
            await this.checkConnection();

            if (this.isConnected) {
                console.log('🚀 开始自动填充表单...');
                await this.fillForm(allData);
            } else {
                this.showStatus('✅ 脚本已生成，请先打开MDAC网站再执行填充', 'success');
            }

        } catch (error) {
            console.error('❌ 生成表单失败:', error);
            this.showStatus(`❌ 生成失败: ${error.message}`, 'error');
        }
    }

    async fillForm(data) {
        if (!data || Object.keys(data).length === 0) {
            this.showStatus('❌ 没有数据可填充', 'error');
            return;
        }

        try {
            this.showStatus('正在填充表单...', 'loading');
            console.log('🔄 开始填充表单，数据:', data);

            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tab) {
                throw new Error('无法获取当前标签页');
            }

            console.log('📋 当前标签页URL:', tab.url);

            // 发送消息到content script进行填充
            console.log('📤 发送填充消息到content script...');
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'fillForm',
                data: data,
                lockedFields: Array.from(this.lockedFields),
                config: this.visionConfig
            });

            console.log('📥 Content script响应:', response);

            if (response && response.success) {
                const result = response.result || {};
                const successCount = result.success ? result.success.length : 0;
                const failedCount = result.failed ? result.failed.length : 0;

                console.log(`✅ 填充成功: ${successCount} 个字段`);
                console.log(`❌ 填充失败: ${failedCount} 个字段`);

                if (result.success && result.success.length > 0) {
                    console.log('成功填充的字段:', result.success);
                }

                if (result.failed && result.failed.length > 0) {
                    console.log('失败的字段:', result.failed);
                }

                this.showStatus(`✅ 表单填充完成，成功 ${successCount} 个，失败 ${failedCount} 个`, 'success');

                // 记录填充历史
                this.recordFillHistory(data, successCount);

            } else {
                const errorMsg = response?.error || '填充失败，请检查网页是否正确加载';
                console.error('❌ 填充失败响应:', response);
                throw new Error(errorMsg);
            }

        } catch (error) {
            console.error('❌ 表单填充错误:', error);
            this.showStatus(`❌ 填充错误: ${error.message}`, 'error');
        }
    }

    async callDoubaoAPI(text, images = []) {
        // 豆包API配置
        const apiKey = '47a1d437-af1e-4833-abc9-82a97235e236';
        const apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        const model = 'doubao-seed-1-6-flash-250828';

        // 构建消息内容
        const messages = [{
            role: 'user',
            content: `请从以下信息中提取MDAC表单字段数据，返回JSON格式：

字段说明：
- name: 姓名（英文全大写）
- passportNo: 护照号
- birthDate: 出生日期 (DD/MM/YYYY格式)
- passportExpiry: 护照有效期 (DD/MM/YYYY格式)
- email: 邮箱地址
- mobile: 手机号码
- nationality: 国籍代码（3位字母，如CHN、USA、MYS等）
- gender: 性别 (MALE或FEMALE)
- arrivalDate: 到达日期 (DD/MM/YYYY格式)
- departureDate: 出发日期 (DD/MM/YYYY格式)
- flightNo: 航班号
- accommodationType: 住宿类型 (HOTEL、RESIDENCE或OTHERS)
- address1: 住宿地址第一行
- address2: 住宿地址第二行
- state: 马来西亚州属
- city: 城市
- postcode: 邮政编码（5位数字）

请只返回JSON格式的数据，不要包含其他文字说明。

信息内容：
${text}`
        }];

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: 0.1,
                    max_tokens: 1500,
                    top_p: 0.9
                })
            });

            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            const content = result.choices?.[0]?.message?.content;

            if (content) {
                // 提取JSON数据
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    try {
                        const data = JSON.parse(jsonMatch[0]);

                        // 数据验证和清理
                        const cleanedData = this.validateAndCleanData(data);

                        return { data: cleanedData };
                    } catch (parseError) {
                        console.error('JSON解析错误:', parseError);
                        throw new Error('API返回的数据格式无效');
                    }
                }
            }

            throw new Error('API未返回有效的JSON数据');

        } catch (error) {
            console.error('❌ API调用错误:', error);
            throw error;
        }
    }

    validateAndCleanData(data) {
        const cleaned = {};

        // 字段验证规则
        const validators = {
            name: (value) => typeof value === 'string' && value.length > 0 ? value.toUpperCase().trim() : null,
            passportNo: (value) => typeof value === 'string' && value.length > 0 ? value.toUpperCase().trim() : null,
            birthDate: (value) => this.validateDate(value),
            passportExpiry: (value) => this.validateDate(value),
            email: (value) => this.validateEmail(value),
            mobile: (value) => typeof value === 'string' && value.length > 0 ? value.replace(/\D/g, '') : null,
            nationality: (value) => typeof value === 'string' && value.length === 3 ? value.toUpperCase() : null,
            gender: (value) => ['MALE', 'FEMALE'].includes(value?.toUpperCase()) ? value.toUpperCase() : null,
            arrivalDate: (value) => this.validateDate(value),
            departureDate: (value) => this.validateDate(value),
            flightNo: (value) => typeof value === 'string' && value.length > 0 ? value.toUpperCase().trim() : null,
            accommodationType: (value) => ['HOTEL', 'RESIDENCE', 'OTHERS'].includes(value?.toUpperCase()) ? value.toUpperCase() : null,
            address1: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            address2: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            state: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            city: (value) => typeof value === 'string' && value.length > 0 ? value.trim() : null,
            postcode: (value) => this.validatePostcode(value)
        };

        // 应用验证规则
        Object.keys(validators).forEach(field => {
            if (data[field] !== undefined && data[field] !== null) {
                const cleanedValue = validators[field](data[field]);
                if (cleanedValue !== null) {
                    cleaned[field] = cleanedValue;
                }
            }
        });

        return cleaned;
    }

    validateDate(dateStr) {
        if (typeof dateStr !== 'string') return null;

        // DD/MM/YYYY格式验证
        const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
        const match = dateStr.match(dateRegex);

        if (match) {
            const [, day, month, year] = match;
            const date = new Date(year, month - 1, day);

            // 验证日期有效性
            if (date.getDate() == day && date.getMonth() == month - 1 && date.getFullYear() == year) {
                return dateStr;
            }
        }

        return null;
    }

    validateEmail(email) {
        if (typeof email !== 'string') return null;

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) ? email.toLowerCase().trim() : null;
    }

    validatePostcode(postcode) {
        if (typeof postcode !== 'string' && typeof postcode !== 'number') return null;

        const postcodeStr = postcode.toString().trim();
        return /^\d{5}$/.test(postcodeStr) ? postcodeStr : null;
    }

    updateFormFields() {
        // 更新侧边栏界面上的表单字段显示 - 使用标准MDAC字段映射
        const fieldMapping = {
            'passengerName': 'name',
            'passportNo': 'passNo',                    // 更新为标准字段名
            'birthDate': 'dob',                       // 更新为标准字段名
            'passportExpiry': 'passExpDte',           // 更新为标准字段名
            'email': 'email',
            'confirmEmail': 'confirmEmail',           // 添加确认邮箱字段
            'phoneRegion': 'region',                  // 修正为实际HTML字段ID
            'phoneNumber': 'mobile',                  // 修正为实际HTML字段ID
            'nationality': 'nationality',
            'gender': 'sex',                          // 更新为标准字段名
            'arrivalDate': 'arrDt',                   // 更新为标准字段名
            'departureDate': 'depDt',                 // 更新为标准字段名
            'flightNo': 'vesselNm',                   // 更新为标准字段名
            'travelMode': 'trvlMode',                 // 添加交通方式字段
            'accommodationType': 'accommodationStay', // 更新为标准字段名
            'address1': 'accommodationAddress1',      // 更新为标准字段名
            'state': 'accommodationState'             // 更新为标准字段名
        };

        Object.keys(fieldMapping).forEach(elementId => {
            const dataKey = fieldMapping[elementId];
            const element = document.getElementById(elementId);

            if (element && this.currentData[dataKey]) {
                element.value = this.currentData[dataKey];

                // 如果字段被锁定，添加视觉指示
                if (this.lockedFields.has(elementId)) {
                    element.classList.add('locked');
                }
            }
        });

        // 更新数据预览
        this.updateDataPreview();
    }

    updateDataPreview() {
        const preview = document.getElementById('dataPreview');
        if (preview) {
            const dataCount = Object.keys(this.currentData).length;
            const dataList = Object.keys(this.currentData).map(key =>
                `${key}: ${this.currentData[key]}`
            ).join('\n');

            preview.innerHTML = `
                <div class="data-summary">已提取 ${dataCount} 个字段</div>
                <div class="data-details">${dataList}</div>
            `;
        }

        // 更新图片预览
        this.updateImagePreview();
    }

    updateImagePreview() {
        const preview = document.getElementById('imagePreview');
        if (preview) {
            if (this.selectedFiles.length > 0) {
                preview.innerHTML = `
                    <div class="image-count">已选择 ${this.selectedFiles.length} 张图片</div>
                    <div class="image-list">
                        ${this.selectedFiles.map(file => `<div class="image-item">${file.name}</div>`).join('')}
                    </div>
                `;
            } else {
                preview.innerHTML = '<div class="no-images">未选择图片</div>';
            }
        }
    }

    async checkConnection() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            console.log('🔍 检查当前标签页:', tab?.url);

            if (tab && tab.url && (
                tab.url.includes('malaysia.travel') ||
                tab.url.includes('mdac') ||
                tab.url.includes('visa.malaysia.gov.my') ||
                tab.url.toLowerCase().includes('digital arrival')
            )) {
                this.isConnected = true;
                this.tabInfo = tab;
                this.showStatus('✅ 已连接到MDAC网站', 'success');
                console.log('✅ MDAC网站连接成功:', tab.url);

                // 尝试注入content script
                try {
                    const response = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
                    console.log('✅ Content script 通信正常:', response);
                } catch (e) {
                    // 如果无法通信，重新注入content script
                    console.log('📝 注入content script...');
                    try {
                        await chrome.scripting.executeScript({
                            target: { tabId: tab.id },
                            files: ['content.js']
                        });
                        console.log('✅ Content script 注入成功');
                    } catch (injectError) {
                        console.error('❌ Content script 注入失败:', injectError);
                    }
                }

            } else {
                this.isConnected = false;
                this.showStatus('⚠️ 请先打开MDAC网站 (malaysia.travel)', 'warning');
                console.log('⚠️ 当前页面不是MDAC网站:', tab?.url);
            }

        } catch (error) {
            this.isConnected = false;
            this.showStatus('❌ 连接检查失败', 'error');
            console.error('连接检查错误:', error);
        }
    }

    async loadSavedData() {
        try {
            const result = await chrome.storage.local.get(['mdacData']);
            if (result.mdacData) {
                this.currentData = result.mdacData;
                this.updateFormFields();
                console.log('✅ 已加载保存的数据');
                this.showStatus('✅ 已加载保存的数据', 'success');
            }
        } catch (error) {
            console.error('❌ 加载数据失败:', error);
        }
    }

    async saveData() {
        try {
            await chrome.storage.local.set({
                mdacData: this.currentData,
                lastSaved: Date.now()
            });
            console.log('✅ 数据已保存');
            this.showStatus('✅ 数据已保存', 'success');
        } catch (error) {
            console.error('❌ 保存数据失败:', error);
            this.showStatus('❌ 保存数据失败', 'error');
        }
    }

    async loadFieldLocks() {
        try {
            const result = await chrome.storage.local.get([this.lockStorageKey]);
            if (result[this.lockStorageKey]) {
                this.lockedFields = new Set(result[this.lockStorageKey]);
                console.log('✅ 已加载字段锁定状态');
            }
        } catch (error) {
            console.error('❌ 加载字段锁定状态失败:', error);
        }
    }

    async saveFieldLocks() {
        try {
            await chrome.storage.local.set({
                [this.lockStorageKey]: Array.from(this.lockedFields)
            });
            console.log('✅ 字段锁定状态已保存');
        } catch (error) {
            console.error('❌ 保存字段锁定状态失败:', error);
        }
    }

    async loadVisionConfig() {
        try {
            const result = await chrome.storage.local.get([this.visionConfigKey]);
            if (result[this.visionConfigKey]) {
                this.visionConfig = { ...this.visionConfig, ...result[this.visionConfigKey] };
                this.applyVisionConfigToUI();
                console.log('✅ 已加载视觉模型配置');
            }
        } catch (error) {
            console.error('❌ 加载视觉配置失败:', error);
        }
    }

    async saveVisionConfig() {
        try {
            await chrome.storage.local.set({
                [this.visionConfigKey]: this.visionConfig
            });
            console.log('✅ 视觉配置已保存');
        } catch (error) {
            console.error('❌ 保存视觉配置失败:', error);
        }
    }

    async loadContactInfo() {
        try {
            const result = await chrome.storage.local.get([this.contactKey]);
            if (result[this.contactKey]) {
                const contactInfo = result[this.contactKey];
                // 自动填充常用联系信息
                this.currentData = { ...contactInfo, ...this.currentData };
                console.log('✅ 已加载联系信息快捷缓存');
            }
        } catch (error) {
            console.error('❌ 加载联系信息失败:', error);
        }
    }

    async saveContactInfo() {
        try {
            const contactFields = ['name', 'email', 'mobile', 'nationality'];
            const contactInfo = {};

            contactFields.forEach(field => {
                if (this.currentData[field]) {
                    contactInfo[field] = this.currentData[field];
                }
            });

            if (Object.keys(contactInfo).length > 0) {
                await chrome.storage.local.set({ [this.contactKey]: contactInfo });
                console.log('✅ 联系信息快捷缓存已更新');
            }
        } catch (error) {
            console.error('❌ 保存联系信息失败:', error);
        }
    }

    loadTheme() {
        try {
            chrome.storage.local.get([this.themeKey]).then(result => {
                const theme = result[this.themeKey] || 'light';
                if (theme === 'dark') {
                    document.body.classList.add('theme-dark');
                } else {
                    document.body.classList.remove('theme-dark');
                }
                const themeBtn = document.getElementById('themeToggleBtn');
                if (themeBtn) {
                    themeBtn.textContent = theme === 'dark' ? 'Dark' : 'Light';
                }
            });
        } catch (error) {
            console.error('loadTheme error:', error);
        }
    }

    toggleTheme() {
        try {
            const isDark = document.body.classList.contains('theme-dark');
            const newTheme = isDark ? 'light' : 'dark';
            if (newTheme === 'dark') {
                document.body.classList.add('theme-dark');
            } else {
                document.body.classList.remove('theme-dark');
            }
            chrome.storage.local.set({ [this.themeKey]: newTheme });
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) {
                themeBtn.textContent = newTheme === 'dark' ? 'Dark' : 'Light';
            }
        } catch (error) {
            console.error('toggleTheme error:', error);
        }
    }

    applyLockedStateToUI() {
        this.lockedFields.forEach(fieldId => {
            this.setFieldLock(fieldId, true);
        });
    }

    applyVisionConfigToUI() {
        // 将视觉配置应用到UI控件
        const confidenceSlider = document.getElementById('confidenceThreshold');
        const mrzCheckbox = document.getElementById('enableMrzOcr');
        const addressCheckbox = document.getElementById('enableAddressEnhance');
        const diffCheckbox = document.getElementById('requireDiffConfirm');

        if (confidenceSlider) confidenceSlider.value = this.visionConfig.confidenceThreshold;
        if (mrzCheckbox) mrzCheckbox.checked = this.visionConfig.enableMrzOcr;
        if (addressCheckbox) addressCheckbox.checked = this.visionConfig.enableAddressEnhance;
        if (diffCheckbox) diffCheckbox.checked = this.visionConfig.requireDiffConfirm;
    }

    toggleFieldLock(fieldId, lockBtnEl = null) {
        const willLock = !this.lockedFields.has(fieldId);
        this.setFieldLock(fieldId, willLock, lockBtnEl);
        this.saveFieldLocks();
        this.showStatus(`${willLock ? 'Locked' : 'Unlocked'} ${fieldId}`, 'info');
    }


    // ????????????? UI
    setFieldLock(fieldId, lock, lockBtnEl = null) {
        const field = document.getElementById(fieldId);
        if (!lockBtnEl) {
            lockBtnEl = document.querySelector(`.lock-btn[data-field="${fieldId}"]`);
        }
        if (lock) {
            this.lockedFields.add(fieldId);
            if (field) {
                field.setAttribute("disabled", "true");
                field.classList.add("locked");
            }
            if (lockBtnEl) {
                lockBtnEl.classList.add("locked");
                lockBtnEl.classList.remove("unlocked");
            }
        } else {
            this.lockedFields.delete(fieldId);
            if (field) {
                field.removeAttribute("disabled");
                field.classList.remove("locked");
            }
            if (lockBtnEl) {
                lockBtnEl.classList.remove("locked");
                lockBtnEl.classList.add("unlocked");
            }
        }
    }

    updateGroupLockButton(btn, locked) {
        if (!btn) return;
        if (locked) {
            btn.classList.add("locked");
            btn.classList.remove("unlocked");
        } else {
            btn.classList.remove("locked");
            btn.classList.add("unlocked");
        }
    }

    startCacheStatsLogging() {
        // 启动缓存统计定时器 - 每分钟记录一次
        this.cacheStatsTimer = setInterval(() => {
            this.logCacheStats();
        }, 60000); // 60秒

        console.log('✅ 缓存统计定时器已启动');
    }

    logCacheStats() {
        try {
            chrome.storage.local.getBytesInUse().then(bytesInUse => {
                console.log(`📊 缓存使用情况: ${bytesInUse} 字节`);

                // 如果缓存使用过多，提醒用户清理
                if (bytesInUse > 1024 * 1024) { // 1MB
                    this.showStatus('⚠️ 缓存使用较多，建议清理', 'warning');
                }
            });
        } catch (error) {
            console.error('❌ 缓存统计失败:', error);
        }
    }

    recordFillHistory(data, filledCount) {
        // 记录填充历史
        const historyEntry = {
            timestamp: Date.now(),
            dataCount: Object.keys(data).length,
            filledCount: filledCount,
            success: filledCount > 0
        };

        chrome.storage.local.get(['fillHistory']).then(result => {
            const history = result.fillHistory || [];
            history.push(historyEntry);

            // 只保留最近100次记录
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            chrome.storage.local.set({ fillHistory: history });
        });
    }

    showStatus(message, type = 'info') {
        const statusEl = document.getElementById('status');
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;

            // 自动清除状态消息
            if (type === 'success' || type === 'error' || type === 'warning') {
                setTimeout(() => {
                    if (statusEl.textContent === message) {
                        statusEl.textContent = '';
                        statusEl.className = 'status';
                    }
                }, 3000);
            }
        }

        // 控制台日志
        const emoji = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            loading: '⏳'
        };

        console.log(`${emoji[type] || 'ℹ️'} ${message}`);
    }

    hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }

    // 图片处理相关方法
    handleImageSelect(e) {
        this.selectedFiles = Array.from(e.target.files);
        this.updateImagePreview();
        console.log(`已选择 ${this.selectedFiles.length} 张图片`);

        // 如果启用了自动处理，立即处理图片
        if (this.selectedFiles.length > 0) {
            this.showStatus(`已选择 ${this.selectedFiles.length} 张图片`, 'info');
        }
    }

    handleImageDrop(e) {
        const files = Array.from(e.dataTransfer.files);

        // 过滤图片文件
        this.selectedFiles = files.filter(file => file.type.startsWith('image/'));
        this.updateImagePreview();

        console.log(`拖拽上传 ${this.selectedFiles.length} 张图片`);
        this.showStatus(`已上传 ${this.selectedFiles.length} 张图片`, 'info');
    }

    // 清理和重置方法
    clearAll() {
        // 清除所有数据和状态
        this.currentData = {};
        this.selectedFiles = [];
        this.lastParsedTextHash = '';

        // 清除定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
            this.autoParseTimer = null;
        }

        // 清除UI
        const travelerInfo = document.getElementById('travelerInfo');
        if (travelerInfo) travelerInfo.value = '';

        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) imageUpload.value = '';

        // 更新显示
        this.updateFormFields();
        this.updateDataPreview();
        this.updateImagePreview();

        this.showStatus('已清除所有数据', 'info');
        console.log('🧹 所有数据已清除');
    }

    fillFormOnly() {
        // 填充示例数据用于测试 - 使用标准MDAC字段映射
        const sampleData = {
            // 基本个人信息
            name: 'ZHANG SAN',
            passNo: 'E12345678',                    // 护照号码 (标准字段名)
            dob: '01/01/1990',                      // 出生日期 (标准字段名)
            passExpDte: '01/01/2030',               // 护照到期日 (标准字段名)
            nationality: 'CHN',                     // 国籍
            sex: 'MALE',                            // 性别 (标准字段名)
            
            // 联系信息
            email: '<EMAIL>',
            confirmEmail: '<EMAIL>',
            region: '86',                           // 电话区号
            mobile: '1234567890',
            
            // 旅行信息
            arrDt: '16/09/2025',                    // 到达日期 (标准字段名)
            depDt: '23/09/2025',                    // 离开日期 (标准字段名)
            vesselNm: 'MH370',                      // 航班号 (标准字段名)
            trvlMode: 'AIR',                        // 交通方式 (标准字段名)
            embark: 'CHN',                          // 出发国家/港口
            
            // 住宿信息
            accommodationStay: 'HOTEL',             // 住宿类型 (标准字段名)
            accommodationAddress1: 'Grand Hyatt Kuala Lumpur', // 地址第一行 (标准字段名)
            accommodationAddress2: 'Jalan Pinang', // 地址第二行 (标准字段名)
            accommodationState: '14',               // 州属代码 (14=吉隆坡)
            accommodationCity: 'KUALA LUMPUR',      // 城市 (标准字段名)
            accommodationPostcode: '50450'          // 邮编 (标准字段名)
        };

        this.currentData = { ...this.currentData, ...sampleData };
        this.updateFormFields();
        this.generateScript();
        this.showStatus('✅ 示例数据已填充', 'success');
        console.log('✅ 示例数据已加载:', sampleData);
    }

    parseContent() {
        // 手动触发内容解析
        const travelerInfo = document.getElementById('travelerInfo')?.value?.trim();

        if (travelerInfo && travelerInfo.length > 10) {
            this.parseTextContent(travelerInfo);
        } else if (this.selectedFiles.length > 0) {
            this.extractDataFromImages(this.selectedFiles);
        } else {
            this.showStatus('❌ 请先输入文本或选择图片', 'error');
        }
    }

    // 脚本操作方法
    copyScript() {
        const scriptArea = document.getElementById('scriptPreview');
        if (scriptArea && scriptArea.textContent) {
            navigator.clipboard.writeText(scriptArea.textContent).then(() => {
                this.showStatus('✅ 脚本已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.showStatus('❌ 复制失败', 'error');
            });
        } else {
            this.showStatus('❌ 没有可复制的脚本', 'error');
        }
    }

    beautifyScript() {
        const scriptArea = document.getElementById('scriptPreview');
        if (scriptArea && scriptArea.textContent) {
            try {
                // 简单的代码格式化
                const script = scriptArea.textContent;
                const formatted = script
                    .replace(/;/g, ';\n')
                    .replace(/{/g, '{\n    ')
                    .replace(/}/g, '\n}')
                    .replace(/,/g, ',\n');

                scriptArea.textContent = formatted;
                this.showStatus('✅ 脚本已格式化', 'success');
            } catch (error) {
                console.error('格式化失败:', error);
                this.showStatus('❌ 格式化失败', 'error');
            }
        } else {
            this.showStatus('❌ 没有可格式化的脚本', 'error');
        }
    }

    minifyScript() {
        const scriptArea = document.getElementById('scriptPreview');
        if (scriptArea && scriptArea.textContent) {
            try {
                // 简单的代码压缩
                const script = scriptArea.textContent;
                const minified = script
                    .replace(/\s+/g, ' ')
                    .replace(/;\s+/g, ';')
                    .replace(/{\s+/g, '{')
                    .replace(/\s+}/g, '}')
                    .trim();

                scriptArea.textContent = minified;
                this.showStatus('✅ 脚本已压缩', 'success');
            } catch (error) {
                console.error('压缩失败:', error);
                this.showStatus('❌ 压缩失败', 'error');
            }
        } else {
            this.showStatus('❌ 没有可压缩的脚本', 'error');
        }
    }

    resetScript() {
        const scriptArea = document.getElementById('scriptPreview');
        if (scriptArea) {
            scriptArea.textContent = '';
            this.showStatus('✅ 脚本已重置', 'success');
        }

        // 如果有数据，重新生成脚本
        if (Object.keys(this.currentData).length > 0) {
            this.generateScript();
        }
    }

    generateScript() {
        // 生成表单填充脚本
        if (Object.keys(this.currentData).length === 0) {
            this.showStatus('❌ 没有数据可生成脚本', 'error');
            return;
        }

        try {
            const script = this.createFillScript(this.currentData);
            const scriptArea = document.getElementById('scriptPreview');
            if (scriptArea) {
                scriptArea.textContent = script;
                scriptArea.classList.remove('hidden'); // 显示脚本预览区域
                this.showStatus('✅ 脚本生成完成', 'success');
                console.log('✅ 脚本已生成并显示');
            } else {
                console.error('❌ 找不到 scriptPreview 元素');
                this.showStatus('❌ 脚本预览区域不可用', 'error');
            }
        } catch (error) {
            console.error('脚本生成失败:', error);
            this.showStatus('❌ 脚本生成失败', 'error');
        }
    }

    createFillScript(data) {
        // 创建表单填充脚本
        const fieldMapping = {
            name: 'passengerName',
            passportNo: 'passportNo',
            birthDate: 'birthDate',
            passportExpiry: 'passportExpiry',
            email: 'email',
            mobile: 'mobile',
            nationality: 'nationality',
            gender: 'gender',
            arrivalDate: 'arrivalDate',
            departureDate: 'departureDate',
            flightNo: 'flightNo',
            accommodationType: 'accommodationType',
            address1: 'address1',
            state: 'state',
            postcode: 'postcode'
        };

        let script = '// MDAC表单自动填充脚本\n';
        script += '(function() {\n';
        script += '    console.log("开始填充MDAC表单...");\n\n';

        Object.keys(data).forEach(key => {
            const fieldId = fieldMapping[key];
            if (fieldId && data[key]) {
                script += `    // 填充${key}\n`;
                script += `    const ${fieldId}Field = document.getElementById('${fieldId}');\n`;
                script += `    if (${fieldId}Field) {\n`;
                script += `        ${fieldId}Field.value = '${data[key]}';\n`;
                script += `        ${fieldId}Field.dispatchEvent(new Event('change'));\n`;
                script += `        console.log('✅ ${key}:', '${data[key]}');\n`;
                script += `    }\n\n`;
            }
        });

        script += '    console.log("✅ MDAC表单填充完成");\n';
        script += '})();';

        return script;
    }

    // 销毁方法 - 清理资源
    destroy() {
        // 清除定时器
        if (this.autoParseTimer) {
            clearTimeout(this.autoParseTimer);
        }

        if (this.cacheStatsTimer) {
            clearInterval(this.cacheStatsTimer);
        }

        console.log('🗑️ MDAC扩展组件已销毁');
    }
}

// 初始化扩展
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 MDAC扩展侧边栏加载中...');

    try {
        window.mdacExtension = new MDACExtension();
        console.log('✅ MDAC扩展初始化完成');
    } catch (error) {
        console.error('❌ MDAC扩展初始化失败:', error);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.mdacExtension) {
        window.mdacExtension.destroy();
    }
});

// Ensure initialization even if DOMContentLoaded already fired
// and expose constructor for integration patches
if (!window.MDACExtension) {
  window.MDACExtension = MDACExtension;
}

(function robustInit() {
  function start() {
    try {
      if (!window.mdacExtension) {
        window.mdacExtension = new MDACExtension();
      }
    } catch (e) {
      console.error('Failed to start MDAC sidepanel:', e);
    }
  }
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', start, { once: true });
  } else {
    start();
  }
})();
