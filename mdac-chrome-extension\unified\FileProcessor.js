// 文件处理扩展模块
// 用途：处理PDF、Office文档等非图片文件
// 依赖：PDF.js（用于PDF处理）
// 技术栈：原生JavaScript + PDF.js
// 核心功能：文件类型识别、PDF转图片、文本提取

class FileProcessor {
  constructor() {
    this.supportedTypes = {
      pdf: ['.pdf', 'application/pdf'],
      text: ['.txt', '.md', '.json', 'text/plain', 'text/markdown', 'application/json'],
      office: ['.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt'],
      image: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    };
  }

  // 批量处理文件（优化内存使用和错误处理）
  async processFiles(files) {
    if (!files || files.length === 0) {
      return { extractedText: '', convertedImages: [], processedFiles: [] };
    }

    const results = {
      extractedText: '',
      convertedImages: [],
      processedFiles: []
    };

    // 限制并发处理数量，避免内存溢出
    const batchSize = 2;

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      const batchPromises = batch.map(file => this.processSingleFile(file));

      try {
        const batchResults = await Promise.allSettled(batchPromises);

        for (const result of batchResults) {
          if (result.status === 'fulfilled' && result.value) {
            const processed = result.value;
            results.processedFiles.push(processed.fileInfo);

            if (processed.text) {
              results.extractedText += `\n\n## 📄 ${processed.fileInfo.name}\n${processed.text}`;
            }

            if (processed.images && processed.images.length > 0) {
              results.convertedImages.push(...processed.images);
            }
          } else if (result.status === 'rejected') {
            console.error('文件处理失败:', result.reason);
          }
        }
      } catch (error) {
        console.error('批次处理失败:', error);
      }

      // 小延迟让浏览器回收内存
      if (i + batchSize < files.length) {
        await this.delay(200);
      }
    }

    return results;
  }

  // 处理单个文件（包装错误处理）
  async processSingleFile(file) {
    try {
      const fileType = this.detectFileType(file);
      const processed = await this.processFileByType(file, fileType);

      if (processed) {
        return {
          fileInfo: {
            name: file.name,
            type: fileType,
            size: file.size,
            processed: true
          },
          ...processed
        };
      }
    } catch (error) {
      console.error(`文件处理失败: ${file.name}`, error);
      // 返回错误信息但不中断整体流程
      return {
        fileInfo: {
          name: file.name,
          type: this.detectFileType(file),
          size: file.size,
          processed: false,
          error: error.message
        },
        text: null,
        images: []
      };
    }

    return null;
  }

  // 延迟函数用于内存管理
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 检测文件类型
  detectFileType(file) {
    const fileName = file.name.toLowerCase();
    const mimeType = file.type.toLowerCase();

    for (const [type, extensions] of Object.entries(this.supportedTypes)) {
      const matches = extensions.some(ext => {
        if (ext.startsWith('.')) {
          return fileName.endsWith(ext);
        }
        return mimeType === ext || mimeType.startsWith(ext);
      });

      if (matches) return type;
    }

    return 'unknown';
  }

  // 根据文件类型处理
  async processFileByType(file, fileType) {
    switch (fileType) {
      case 'pdf':
        return await this.processPDFFile(file);
      case 'text':
        return await this.processTextFile(file);
      case 'office':
        return await this.processOfficeFile(file);
      case 'image':
        // 图片文件交由ImageProcessor处理
        return null;
      default:
        throw new Error(`不支持的文件类型: ${fileType}`);
    }
  }

  // 处理PDF文件（增强版本 - 支持更多页面和更好的降级处理）
  async processPDFFile(file) {
    let pdf = null;
    let objectUrl = null;

    try {
      // 检查是否已加载PDF.js
      if (typeof pdfjsLib === 'undefined') {
        console.warn('PDF.js未加载，使用基础信息提取');
        return {
          text: `PDF文件: ${file.name}\n文件大小: ${this.formatFileSize(file.size)}\n页数: 未知\n注意: 需要加载PDF.js库才能完整处理PDF内容`,
          images: []
        };
      }

      const arrayBuffer = await file.arrayBuffer();
      pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      let extractedText = '';
      const convertedImages = [];

      // 根据文件大小和页数动态调整处理限制
      const fileSizeMB = file.size / (1024 * 1024);
      const totalPages = pdf.numPages;

      // 智能页面限制：大文件或少页面时处理更多内容
      let maxPages;
      if (fileSizeMB < 1) {
        maxPages = Math.min(totalPages, 10); // 小文件处理最多10页
      } else if (fileSizeMB < 5) {
        maxPages = Math.min(totalPages, 5);  // 中等文件处理最多5页
      } else {
        maxPages = Math.min(totalPages, 3);  // 大文件处理最多3页
      }

      console.log(`📄 处理PDF文件: ${file.name}, 总页数: ${totalPages}, 将处理: ${maxPages}页`);

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        let page = null;
        let canvas = null;

        try {
          page = await pdf.getPage(pageNum);

          // 提取文本内容
          const textContent = await page.getTextContent();
          const pageText = textContent.items.map(item => item.str).join(' ');
          if (pageText.trim()) {
            extractedText += `\n### 第${pageNum}页\n${pageText}\n`;
          }

          // 提取页面元数据
          const pageMetadata = {
            pageNumber: pageNum,
            width: page.view[2],
            height: page.view[3],
            hasText: pageText.trim().length > 0,
            textLength: pageText.trim().length
          };

          // 转换为图片（可选，根据内存和性能考虑）
          try {
            // 根据页面大小调整缩放比例
            const scale = Math.min(1.0, 1000 / Math.max(pageMetadata.width, pageMetadata.height));

            canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            const viewport = page.getViewport({ scale: scale });

            canvas.height = viewport.height;
            canvas.width = viewport.width;

            await page.render({ canvasContext: context, viewport }).promise;
            const imageData = canvas.toDataURL('image/jpeg', 0.85);
            const base64 = imageData.split(',')[1];

            convertedImages.push({
              name: `${file.name}_page_${pageNum}.jpg`,
              originalName: file.name,
              mimeType: 'image/jpeg',
              base64: base64,
              size: Math.round(base64.length * 0.75), // 估算大小
              pageNumber: pageNum,
              width: canvas.width,
              height: canvas.height,
              scale: scale,
              metadata: pageMetadata
            });

            console.log(`✅ 第${pageNum}页处理完成: ${canvas.width}x${canvas.height}, 文本长度: ${pageMetadata.textLength}`);

          } catch (renderError) {
            console.warn(`PDF页面${pageNum}渲染失败:`, renderError);
            // 即使渲染失败，也继续处理文本内容
          }

        } finally {
          // 清理资源
          if (page) {
            try {
              page.cleanup();
            } catch (e) {
              console.warn(`清理页面${pageNum}失败:`, e);
            }
          }
          if (canvas) {
            canvas.width = 0;
            canvas.height = 0;
            canvas = null;
          }

          // 小延迟让浏览器回收内存，根据文件大小调整延迟
          const memoryDelay = fileSizeMB < 1 ? 50 : 100;
          if (pageNum < maxPages) {
            await this.delay(memoryDelay);
          }
        }
      }

      // 生成处理摘要
      const processingSummary = {
        totalPages: totalPages,
        processedPages: maxPages,
        hasText: extractedText.trim().length > 0,
        textLength: extractedText.trim().length,
        imagesGenerated: convertedImages.length,
        fileSizeMB: fileSizeMB
      };

      const resultText = extractedText.trim() || `PDF文件: ${file.name}\n总页数: ${totalPages}\n已处理: ${maxPages}页\n文件大小: ${this.formatFileSize(file.size)}\n处理摘要: ${JSON.stringify(processingSummary, null, 2)}`;

      console.log(`📊 PDF处理完成:`, processingSummary);

      return {
        text: resultText,
        images: convertedImages,
        summary: processingSummary
      };

    } catch (error) {
      console.error('PDF处理失败:', error);
      return {
        text: `PDF文件: ${file.name}\n处理失败: ${error.message}`,
        images: []
      };
    } finally {
      // 清理PDF资源
      if (pdf) {
        try {
          pdf.destroy();
        } catch (e) {
          console.warn('PDF销毁失败:', e);
        }
      }
    }
  }

  // 处理文本文件
  async processTextFile(file) {
    try {
      const text = await file.text();
      const cleanText = text.trim();

      if (!cleanText) {
        return { text: `文本文件: ${file.name}（文件为空）`, images: [] };
      }

      // 根据文件扩展名添加格式标识
      const ext = file.name.toLowerCase().split('.').pop();
      let formattedText = cleanText;

      if (ext === 'json') {
        try {
          const jsonData = JSON.parse(cleanText);
          formattedText = '```json\n' + JSON.stringify(jsonData, null, 2) + '\n```';
        } catch {
          formattedText = '```\n' + cleanText + '\n```';
        }
      } else if (ext === 'md') {
        // Markdown文件保持原格式
        formattedText = cleanText;
      } else {
        formattedText = '```\n' + cleanText + '\n```';
      }

      return {
        text: formattedText,
        images: []
      };
    } catch (error) {
      return {
        text: `文本文件: ${file.name}\n读取失败: ${error.message}`,
        images: []
      };
    }
  }

  // 处理Office文档（增强实现 - 支持基础内容提取）
  async processOfficeFile(file) {
    const fileName = file.name.toLowerCase();
    const fileSize = this.formatFileSize(file.size);

    try {
      // 尝试读取文件头进行基础分析
      const arrayBuffer = await file.arrayBuffer();
      const bytes = new Uint8Array(arrayBuffer.slice(0, 100)); // 读取前100字节

      // 基础文件分析
      let fileInfo = {
        name: file.name,
        size: fileSize,
        type: 'unknown',
        hasMacros: false,
        encrypted: false
      };

      // 检测文件类型和基础属性
      if (fileName.endsWith('.docx')) {
        fileInfo.type = 'Word文档 (DOCX)';
        // DOCX文件是ZIP格式，可以尝试解压获取基本信息
        try {
          // 检查是否为有效的ZIP文件（DOCX的基础格式）
          const isZip = bytes[0] === 0x50 && bytes[1] === 0x4B; // PK签名
          if (isZip) {
            fileInfo.structure = 'Open XML格式（基于ZIP）';
          }
        } catch (e) {
          console.warn('DOCX基础分析失败:', e);
        }
      } else if (fileName.endsWith('.xlsx')) {
        fileInfo.type = 'Excel表格 (XLSX)';
        const isZip = bytes[0] === 0x50 && bytes[1] === 0x4B;
        if (isZip) {
          fileInfo.structure = 'Open XML格式（基于ZIP）';
        }
      } else if (fileName.endsWith('.pptx')) {
        fileInfo.type = 'PowerPoint演示文稿 (PPTX)';
        const isZip = bytes[0] === 0x50 && bytes[1] === 0x4B;
        if (isZip) {
          fileInfo.structure = 'Open XML格式（基于ZIP）';
        }
      } else if (fileName.endsWith('.doc')) {
        fileInfo.type = 'Word文档 (DOC)';
        // 检查是否为复合文档格式
        const isCompound = bytes[0] === 0xD0 && bytes[1] === 0xCF && bytes[2] === 0x11 && bytes[3] === 0xE0;
        if (isCompound) {
          fileInfo.structure = '复合文档格式';
        }
      } else if (fileName.endsWith('.xls')) {
        fileInfo.type = 'Excel表格 (XLS)';
        const isCompound = bytes[0] === 0xD0 && bytes[1] === 0xCF && bytes[2] === 0x11 && bytes[3] === 0xE0;
        if (isCompound) {
          fileInfo.structure = '复合文档格式';
        }
      } else if (fileName.endsWith('.ppt')) {
        fileInfo.type = 'PowerPoint演示文稿 (PPT)';
        const isCompound = bytes[0] === 0xD0 && bytes[1] === 0xCF && bytes[2] === 0x11 && bytes[3] === 0xE0;
        if (isCompound) {
          fileInfo.structure = '复合文档格式';
        }
      }

      // 生成详细的分析报告
      const analysisText = `## 📄 Office文档分析报告

### 基本信息
- **文件名**: ${file.name}
- **文件大小**: ${fileSize}
- **文档类型**: ${fileInfo.type}
- **文件格式**: ${fileInfo.structure || '未知'}

### 处理状态
- **状态**: 📋 基础信息已提取
- **详细内容**: 需要专业的Office文档解析库才能提取完整内容

### 建议
1. 对于Word文档：可以提取文本内容、表格、图片等
2. 对于Excel表格：可以提取工作表数据、图表、公式等
3. 对于PowerPoint：可以提取幻灯片文本、图片、备注等

### 下一步
如需完整的文档内容解析，建议：
- 将文档转换为PDF格式后重新上传
- 或者手动复制文档中的关键信息到文本输入框

---
*这是基础分析报告，完整的Office文档解析功能正在开发中。*`;

      return {
        text: analysisText,
        images: [],
        metadata: fileInfo
      };

    } catch (error) {
      console.error('Office文件基础分析失败:', error);
      return {
        text: `Office文件: ${file.name}\n文件大小: ${fileSize}\n处理状态: 基础信息提取失败\n错误: ${error.message}`,
        images: [],
        error: error.message
      };
    }
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 获取文件处理统计信息
  getProcessingStats(processedFiles) {
    if (!processedFiles || processedFiles.length === 0) {
      return { totalFiles: 0, successCount: 0, failureCount: 0 };
    }

    const successCount = processedFiles.filter(f => f.processed).length;
    const failureCount = processedFiles.filter(f => !f.processed).length;

    return {
      totalFiles: processedFiles.length,
      successCount,
      failureCount,
      successRate: Math.round((successCount / processedFiles.length) * 100)
    };
  }

  // 验证文件处理结果
  validateProcessing(result) {
    if (!result) return false;
    return result.extractedText.length > 0 || result.convertedImages.length > 0;
  }
}

// 全局暴露
window.FileProcessor = FileProcessor;