﻿// ç»Ÿä¸€ç”¨æˆ·ç•Œé¢æŽ§åˆ¶å™¨
// ç”¨é€”ï¼šç®¡ç†ç»Ÿä¸€çš„å¤šæ¨¡æ€ä¸Šä¼ ç•Œé¢å’Œç”¨æˆ·äº¤äº’
// ä¾èµ–ï¼šUnifiedMultiModalProcessor.js, ImageProcessor.js, FileProcessor.js
// æŠ€æœ¯æ ˆï¼šåŽŸç”ŸJavaScript + HTML5æ‹–æ‹½API
// æ ¸å¿ƒåŠŸèƒ½ï¼šæ–‡ä»¶ä¸Šä¼ æŽ§åˆ¶ã€è¿›åº¦æ˜¾ç¤ºã€ç»“æžœå±•ç¤ºã€ç”¨æˆ·åé¦ˆ

class UIController {
  constructor() {
    // 严格单例：通过工厂方法获取同一实例
    this.processor = UnifiedMultiModalProcessor.getInstance();
    this.currentFiles = {
      images: [],
      documents: [],
      textFiles: []
    };
    this.isProcessing = false;
    this.initializeUI();
  }

  // åˆå§‹åŒ–ç”¨æˆ·ç•Œé¢
  initializeUI() {
    this.setupUploadIntegration();
    this.setupEventListeners();
    this.setupProgressDisplay();
  }

  // é›†æˆåˆ°çŽ°æœ‰çš„uploadDropzone
  setupUploadIntegration() {
    // æŸ¥æ‰¾çŽ°æœ‰çš„ä¸Šä¼ æŒ‰é’®
    const existingUploadBtn = document.getElementById('uploadDropzone');
    if (existingUploadBtn) {
      // åˆ›å»ºæ–‡ä»¶è¾“å…¥å…ƒç´ 
      if (!document.getElementById('unifiedFileInput')) {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'unifiedFileInput';
        fileInput.multiple = true;
        fileInput.accept = 'image/*,.pdf,.txt,.md,.json,.doc,.docx';
        fileInput.style.display = 'none';

        // æ’å…¥åˆ°ä¸Šä¼ æŒ‰é’®åŽé¢
        existingUploadBtn.parentNode.insertBefore(fileInput, existingUploadBtn.nextSibling);
      }

      // æ·»åŠ ä¸Šä¼ çŠ¶æ€æ˜¾ç¤ºåŒºåŸŸ
      if (!document.getElementById('uploadStatusArea')) {
        const statusArea = document.createElement('div');
        statusArea.id = 'uploadStatusArea';
        statusArea.className = 'upload-status-area';
        statusArea.style.display = 'none';
        statusArea.innerHTML = this.getUploadStatusHTML();

        // æ’å…¥åˆ°è¾“å…¥åŒºåŸŸä¸‹æ–¹
        const inputSection = document.querySelector('.input-group') || document.getElementById('statusArea');
        if (inputSection) {
          inputSection.appendChild(statusArea);
        }
      }
    }
  }

  // èŽ·å–ä¸Šä¼ çŠ¶æ€HTML
  getUploadStatusHTML() {
    return `
      <!-- æ–‡ä»¶åˆ—è¡¨æ˜¾ç¤º -->
      <div class="file-list" id="fileList" style="display:none;">
        <h4>ðŸ“‹ å·²é€‰æ‹©æ–‡ä»¶:</h4>
        <div class="file-categories">
          <div class="file-category" id="imageFiles">
            <h5>ðŸ–¼ï¸ å›¾ç‰‡æ–‡ä»¶</h5>
            <div class="file-items"></div>
          </div>
          <div class="file-category" id="documentFiles">
            <h5>ðŸ“„ æ–‡æ¡£æ–‡ä»¶</h5>
            <div class="file-items"></div>
          </div>
          <div class="file-category" id="textFiles">
            <h5>ðŸ“ æ–‡æœ¬æ–‡ä»¶</h5>
            <div class="file-items"></div>
          </div>
        </div>
        <div class="file-actions">
          <button id="clearFilesBtn" type="button" class="secondary-button">ðŸ—‘ï¸ æ¸…ç©ºæ–‡ä»¶</button>
        </div>
      </div>

      <!-- è¿›åº¦æ˜¾ç¤º -->
      <div class="progress-area" id="progressArea" style="display:none;">
        <div class="progress-header">
          <h4>â³ å¤„ç†è¿›åº¦</h4>
          <div class="progress-stats" id="progressStats"></div>
        </div>
        <div class="progress-steps" id="progressSteps"></div>
      </div>

      <!-- ç»“æžœæ˜¾ç¤º -->
      <div class="result-area" id="resultArea" style="display:none;">
        <div class="result-header">
          <h4>âœ… å¤„ç†ç»“æžœ</h4>
          <div class="result-actions">
            <button id="copyResultBtn" type="button">ðŸ“‹ å¤åˆ¶</button>
            <button id="fillFormBtn" type="button">ðŸ“ å¡«å…¥è¡¨å•</button>
          </div>
        </div>
        <div class="result-content" id="resultContent"></div>
      </div>
    `;
  }

  // è®¾ç½®äº‹ä»¶ç›‘å¬å™¨
  setupEventListeners() {
    // çŽ°æœ‰çš„ä¸Šä¼ æŒ‰é’®
    const uploadBtn = document.getElementById('uploadDropzone');
    const fileInput = document.getElementById('unifiedFileInput');

    if (uploadBtn && fileInput) {
      uploadBtn.addEventListener('click', () => fileInput.click());
      fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    // æ–‡ä»¶æ‹–æ‹½åˆ°æ•´ä¸ªè¾“å…¥åŒºåŸŸ
    const inputSection = document.querySelector('.input-group') || document.getElementById('statusArea');
    if (inputSection) {
      inputSection.addEventListener('dragover', this.handleDragOver.bind(this));
      inputSection.addEventListener('drop', this.handleDrop.bind(this));
      inputSection.addEventListener('dragleave', this.handleDragLeave.bind(this));
    }

    // æ¸…ç©ºæ–‡ä»¶æŒ‰é’®
    const clearFilesBtn = document.getElementById('clearFilesBtn');
    if (clearFilesBtn) {
      clearFilesBtn.addEventListener('click', this.handleClearFiles.bind(this));
    }

    // ç»“æžœæ“ä½œæŒ‰é’®
    const copyBtn = document.getElementById('copyResultBtn');
    const fillBtn = document.getElementById('fillFormBtn');
    if (copyBtn) copyBtn.addEventListener('click', this.handleCopyResult.bind(this));
    if (fillBtn) fillBtn.addEventListener('click', this.handleFillForm.bind(this));
  }

  // è®¾ç½®è¿›åº¦æ˜¾ç¤º
  setupProgressDisplay() {
    // å¦‚æžœå­˜åœ¨performance monitorï¼Œè¿žæŽ¥å…¶äº‹ä»¶
    if (window.performanceMonitor) {
      // å¯ä»¥åœ¨è¿™é‡Œè®¾ç½®è¿›åº¦æ›´æ–°å›žè°ƒ
      this.connectPerformanceMonitor();
    }
  }

  // å¤„ç†æ‹–æ‹½äº‹ä»¶
  handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');
  }

  handleDragLeave(event) {
    event.currentTarget.classList.remove('drag-over');
  }

  handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');

    const files = Array.from(event.dataTransfer.files);
    this.processSelectedFiles(files);
  }

  // å¤„ç†æ–‡ä»¶é€‰æ‹©
  handleFileSelect(event) {
    const files = Array.from(event.target.files);
    this.processSelectedFiles(files);
    event.target.value = ''; // é‡ç½®input
  }

  // å¤„ç†é€‰æ‹©çš„æ–‡ä»¶
  processSelectedFiles(files) {
    files.forEach(file => {
      const category = this.categorizeFile(file);
      this.currentFiles[category].push(file);
    });

    this.updateFileDisplay();
    this.updateProcessButton();
  }

  // æ–‡ä»¶åˆ†ç±»
  categorizeFile(file) {
    if (file.type.startsWith('image/')) return 'images';
    if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) return 'documents';
    if (file.type.startsWith('text/') || ['.txt', '.md', '.json'].some(ext => file.name.toLowerCase().endsWith(ext))) return 'textFiles';
    return 'documents'; // é»˜è®¤å½’ç±»ä¸ºæ–‡æ¡£
  }

  // æ›´æ–°æ–‡ä»¶æ˜¾ç¤º
  updateFileDisplay() {
    const fileList = document.getElementById('fileList');
    if (!fileList) { return; }
    const hasFiles = Object.values(this.currentFiles).some(arr => arr.length > 0);

    if (hasFiles) {
      fileList.style.display = 'block';
      this.updateFileCategory('images', 'ðŸ–¼ï¸ å›¾ç‰‡æ–‡ä»¶');
      this.updateFileCategory('documents', 'ðŸ“„ æ–‡æ¡£æ–‡ä»¶');
      this.updateFileCategory('textFiles', 'ðŸ“ æ–‡æœ¬æ–‡ä»¶');
    } else {
      fileList.style.display = 'none';
    }
  }

  // æ›´æ–°æ–‡ä»¶åˆ†ç±»æ˜¾ç¤º
  updateFileCategory(category, title) {
    const categoryEl = document.getElementById(category === 'images' ? 'imageFiles' :
                                            category === 'documents' ? 'documentFiles' : 'textFiles');
    const itemsContainer = categoryEl?.querySelector('.file-items');

    if (!itemsContainer) return;

    const files = this.currentFiles[category];

    if (files.length === 0) {
      categoryEl.style.display = 'none';
      return;
    }

    categoryEl.style.display = 'block';
    itemsContainer.innerHTML = files.map((file, index) => `
      <div class="file-item" data-category="${category}" data-index="${index}">
        <span class="file-name">${file.name}</span>
        <span class="file-size">(${this.formatFileSize(file.size)})</span>
        <button class="remove-file" onclick="window.uiController.removeFile('${category}', ${index})">âŒ</button>
      </div>
    `).join('');
  }

  // ç§»é™¤æ–‡ä»¶
  removeFile(category, index) {
    this.currentFiles[category].splice(index, 1);
    this.updateFileDisplay();
    this.updateProcessButton();
  }

  // æ›´æ–°å¤„ç†æŒ‰é’®çŠ¶æ€
  updateProcessButton() {
    const processBtn = document.getElementById('processBtn');
    const hasContent = this.hasValidInput();

    if (processBtn) {
      processBtn.disabled = !hasContent || this.isProcessing;
      processBtn.textContent = this.isProcessing ? 'â³ å¤„ç†ä¸­...' : 'ðŸš€ å¼€å§‹å¤„ç†';
    }
  }

  // æ£€æŸ¥æ˜¯å¦æœ‰æœ‰æ•ˆè¾“å…¥
  hasValidInput() {
    const textInput = document.getElementById('userTextInput')?.value?.trim();
    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);

    return textInput || totalFiles > 0;
  }

  // å¤„ç†AIè§£æžï¼ˆé›†æˆåˆ°çŽ°æœ‰æŒ‰é’®ï¼‰
  async handleAIParseWithFiles() {
    if (this.isProcessing) return;

    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    if (totalFiles === 0) {
      // å¦‚æžœæ²¡æœ‰æ–‡ä»¶ï¼Œè°ƒç”¨åŽŸæœ‰çš„AIè§£æžé€»è¾‘
      return;
    }

    this.isProcessing = true;
    this.showProgressArea();

    try {
      const inputs = this.collectInputs();
      const sessionId = `multimodal_${Date.now()}`;

      const result = await this.processor.processMultiModalInput({
        ...inputs,
        sessionId
      });

      this.showResult(result);
    } catch (error) {
      this.showError(error);
    } finally {
      this.isProcessing = false;
      this.hideProgressArea();
    }
  }

  // æ£€æŸ¥æ˜¯å¦åº”è¯¥ä½¿ç”¨å¤šæ¨¡æ€å¤„ç†
  shouldUseMultiModalProcessing() {
    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    return totalFiles > 0;
  }

  // æ”¶é›†æ‰€æœ‰è¾“å…¥
  collectInputs() {
    // ä½¿ç”¨çŽ°æœ‰çš„æ–‡å­—è¾“å…¥æ¡†
    const textInput = document.getElementById('travelerInfo')?.value?.trim() || '';
    const allImages = this.currentFiles.images;
    const allFiles = [...this.currentFiles.documents, ...this.currentFiles.textFiles];

    return {
      textInput,
      images: allImages,
      files: allFiles,
      systemPrompt: null // ä½¿ç”¨é»˜è®¤çš„MDACæå–æç¤ºè¯
    };
  }

  // æ˜¾ç¤ºè¿›åº¦åŒºåŸŸ
  showProgressArea() {
    const progressArea = document.getElementById('progressArea');
    if (progressArea) {
      progressArea.style.display = 'block';
      this.updateProgressStats('å¼€å§‹å¤„ç†...');
    }
  }

  // éšè—è¿›åº¦åŒºåŸŸ
  hideProgressArea() {
    const progressArea = document.getElementById('progressArea');
    if (progressArea) {
      progressArea.style.display = 'none';
    }
  }

  // æ›´æ–°è¿›åº¦ç»Ÿè®¡
  updateProgressStats(message) {
    const progressStats = document.getElementById('progressStats');
    if (progressStats) {
      progressStats.textContent = message;
    }
  }

  // æ˜¾ç¤ºç»“æžœ
  showResult(result) {
    const resultArea = document.getElementById('resultArea');
    const resultContent = document.getElementById('resultContent');

    if (resultArea && resultContent) {
      resultArea.style.display = 'block';

      if (result.success && result.data) {
        resultContent.innerHTML = `
          <div class="result-json">
            <pre>${JSON.stringify(result.data, null, 2)}</pre>
          </div>
          ${result.metadata ? `<div class="result-metadata">
            <small>å¤„ç†ç»Ÿè®¡: ${this.formatMetadata(result.metadata)}</small>
          </div>` : ''}
        `;
      } else {
        resultContent.innerHTML = `
          <div class="result-raw">
            <h5>åŽŸå§‹å“åº”:</h5>
            <pre>${result.rawResponse || 'æ— å“åº”å†…å®¹'}</pre>
          </div>
        `;
      }
    }
  }

  // æ˜¾ç¤ºé”™è¯¯
  showError(error) {
    const resultArea = document.getElementById('resultArea');
    const resultContent = document.getElementById('resultContent');

    if (resultArea && resultContent) {
      resultArea.style.display = 'block';
      resultContent.innerHTML = `
        <div class="error-message">
          <h5>âŒ å¤„ç†å¤±è´¥</h5>
          <p>${error.message || 'æœªçŸ¥é”™è¯¯'}</p>
        </div>
      `;
    }
  }

  // æ¸…ç©ºæ–‡ä»¶
  handleClearFiles() {
    // æ¸…ç©ºæ–‡ä»¶
    this.currentFiles = { images: [], documents: [], textFiles: [] };

    // éšè—æ–‡ä»¶åˆ—è¡¨
    const fileList = document.getElementById('fileList');
    if (fileList) fileList.style.display = 'none';

    // éšè—ç»“æžœåŒºåŸŸ
    const resultArea = document.getElementById('resultArea');
    if (resultArea) resultArea.style.display = 'none';

    this.showToast('ðŸ—‘ï¸ å·²æ¸…ç©ºæ‰€æœ‰æ–‡ä»¶');
  }

  // å¤åˆ¶ç»“æžœ
  handleCopyResult() {
    const resultContent = document.querySelector('#resultArea .result-json pre');
    if (resultContent) {
      navigator.clipboard.writeText(resultContent.textContent).then(() => {
        this.showToast('âœ… ç»“æžœå·²å¤åˆ¶åˆ°å‰ªè´´æ¿');
      });
    }
  }

  // å¡«å…¥è¡¨å•
  handleFillForm() {
    // è¿™é‡Œéœ€è¦ä¸ŽçŽ°æœ‰çš„è¡¨å•å¡«å……é€»è¾‘é›†æˆ
    this.showToast('ðŸš§ è¡¨å•å¡«å……åŠŸèƒ½å¼€å‘ä¸­...');
  }

  // å·¥å…·æ–¹æ³•
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatMetadata(metadata) {
    const parts = [];
    if (metadata.imageStats) parts.push(`å›¾ç‰‡:${metadata.imageStats.totalFiles}`);
    if (metadata.fileStats) parts.push(`æ–‡ä»¶:${metadata.fileStats.totalFiles}`);
    if (metadata.inputSummary) parts.push(`æ–‡å­—:${metadata.inputSummary.textLength}å­—ç¬¦`);
    return parts.join(', ');
  }

  showToast(message) {
    // ç®€å•çš„æ¶ˆæ¯æç¤º
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed; top: 20px; right: 20px; z-index: 10000;
      background: #4CAF50; color: white; padding: 12px 20px;
      border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
  }

  // è¿žæŽ¥æ€§èƒ½ç›‘æŽ§å™¨ï¼ˆå¦‚æžœå¯ç”¨ï¼‰
  connectPerformanceMonitor() {
    // å¯ä»¥åœ¨è¿™é‡Œæ·»åŠ è¿›åº¦æ›´æ–°çš„å›žè°ƒ
  }

  // ä¸ŽçŽ°æœ‰AIè§£æžé›†æˆçš„å…¥å£æ–¹æ³•
  integrateWithExistingAIParse() {
    // æ£€æŸ¥æ˜¯å¦æœ‰æ–‡ä»¶éœ€è¦å¤„ç†
    if (this.shouldUseMultiModalProcessing()) {
      return this.handleAIParseWithFiles();
    }
    return false; // è®©åŽŸæœ‰é€»è¾‘å¤„ç†
  }

  // èŽ·å–ç»Ÿä¸€å¤„ç†å™¨å®žä¾‹ï¼ˆä¾›å¤–éƒ¨è°ƒç”¨ï¼‰
  getProcessor() {
    return this.processor;
  }

  // èŽ·å–å½“å‰æ–‡ä»¶çŠ¶æ€ï¼ˆä¾›å¤–éƒ¨è°ƒç”¨ï¼‰
  getFileStatus() {
    const totalFiles = Object.values(this.currentFiles).reduce((sum, arr) => sum + arr.length, 0);
    return {
      hasFiles: totalFiles > 0,
      totalFiles,
      fileCount: this.currentFiles,
      shouldUseMultiModal: this.shouldUseMultiModalProcessing()
    };
  }
}

// å…¨å±€æš´éœ²å’Œè‡ªåŠ¨åˆå§‹åŒ–
window.UIController = UIController;

// é¡µé¢åŠ è½½å®ŒæˆåŽè‡ªåŠ¨åˆå§‹åŒ–
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.uiController = new UIController();
  });
} else {
  window.uiController = new UIController();
}
