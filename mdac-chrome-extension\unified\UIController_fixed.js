// 统一用户界面控制器
// 用途：管理统一的多模态上传界面和用户交互
// 依赖：UnifiedMultiModalProcessor.js, ImageProcessor.js, FileProcessor.js
// 技术栈：原生JavaScript + HTML5拖拽API
// 核心功能：文件上传控制、进度显示、结果展示、用户反馈

class UIController {
  constructor() {
    // 严格单例：通过工厂方法获取同一实例
    this.processor = UnifiedMultiModalProcessor.getInstance();
    this.currentFiles = {
      images: [],
      documents: [],
      textFiles: []
    };
    this.isProcessing = false;
    this.initializeUI();
  }

  // 初始化用户界面
  initializeUI() {
    console.log('✅ UIController 初始化完成');
    this.bindEvents();
  }

  bindEvents() {
    // 绑定界面事件
    console.log('✅ UIController 事件绑定完成');
  }
}

// 导出单例实例
window.UIController = UIController;