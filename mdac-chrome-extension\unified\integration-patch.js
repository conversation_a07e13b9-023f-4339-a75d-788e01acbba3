// 统一多模态系统与现有系统集成补丁
// 用途：修改现有的AI解析逻辑，支持文件上传的多模态处理
// 依赖：UIController.js, UnifiedMultiModalProcessor.js
// 技术栈：原生JavaScript
// 集成点：sidepanel.js 中的 handleAIParse 方法

(function() {
  'use strict';

  // 等待页面和UIController初始化完成
  function waitForInitialization() {
    return new Promise((resolve) => {
      const checkInit = () => {
        if (window.uiController && window.MDACExtension) {
          resolve();
        } else {
          setTimeout(checkInit, 100);
        }
      };
      checkInit();
    });
  }

  // 重写MDACExtension的handleAIParse方法
  async function patchHandleAIParse() {
    await waitForInitialization();

    const originalHandleAIParse = window.MDACExtension.prototype.handleAIParse;

    window.MDACExtension.prototype.handleAIParse = async function() {
      // 检查是否有上传的文件需要处理
      const fileStatus = window.uiController.getFileStatus();

      if (fileStatus.shouldUseMultiModal) {
        console.log('🔄 使用统一多模态处理器处理文件');

        try {
          // 使用统一多模态处理器
          await window.uiController.integrateWithExistingAIParse();
        } catch (error) {
          console.error('❌ 多模态处理失败，回退到原有逻辑:', error);
          // 回退到原有逻辑
          return originalHandleAIParse.call(this);
        }
      } else {
        // 没有文件，使用原有的文本处理逻辑
        console.log('📝 使用原有文本处理逻辑');
        return originalHandleAIParse.call(this);
      }
    };

    console.log('✅ AI解析方法已成功集成多模态处理');
  }

  // 扩展结果处理，支持统一系统的结果显示
  async function enhanceResultHandling() {
    await waitForInitialization();

    // 监听统一系统的结果显示
    const resultObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.target.id === 'resultArea' &&
            mutation.type === 'attributes' &&
            mutation.attributeName === 'style') {

          const resultArea = mutation.target;
          if (resultArea.style.display !== 'none') {
            // 结果显示时，尝试自动填入表单
            const resultContent = resultArea.querySelector('.result-json pre');
            if (resultContent) {
              try {
                const data = JSON.parse(resultContent.textContent);
                if (data && typeof data === 'object') {
                  // 验证数据映射准确性
                  if (window.fieldMappingValidator) {
                    const validation = window.fieldMappingValidator.validateDataMapping(data);
                    console.log('🔍 数据映射验证结果:', validation);

                    if (!validation.success) {
                      console.warn('⚠️ 数据映射存在问题:', validation.details);
                    }
                  }

                  // 自动填入表单数据
                  window.MDACExtension.prototype.applyFieldUpdates.call(
                    window.mdacExtension, data
                  );
                  console.log('✅ 多模态解析结果已自动填入表单');
                }
              } catch (error) {
                console.warn('⚠️ 解析结果格式错误:', error);
              }
            }
          }
        }
      });
    });

    const resultArea = document.getElementById('resultArea');
    if (resultArea) {
      resultObserver.observe(resultArea, {
        attributes: true,
        attributeFilter: ['style']
      });
    }
  }

  // 增强上传按钮的提示
  function enhanceUploadButtonFeedback() {
    const uploadBtn = document.getElementById('uploadDropzone');
    if (!uploadBtn) return;

    // 添加文件计数提示
    function updateButtonText() {
      if (!window.uiController) return;

      const fileStatus = window.uiController.getFileStatus();
      const baseText = '上传文件';

      if (fileStatus.hasFiles) {
        uploadBtn.textContent = `📁 ${baseText} (${fileStatus.totalFiles})`;
        uploadBtn.classList.add('has-files');
      } else {
        uploadBtn.textContent = `📁 ${baseText}`;
        uploadBtn.classList.remove('has-files');
      }
    }

    // 定期更新按钮状态
    setInterval(updateButtonText, 500);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      #uploadDropzone.has-files {
        background-color: var(--accent-color);
        color: white;
      }

      .upload-status-area {
        margin-top: 15px;
        padding: 15px;
        background: var(--bg-secondary);
        border-radius: 6px;
        border: 1px solid var(--border-color);
      }

      .file-actions {
        margin-top: 10px;
        text-align: right;
      }
    `;
    document.head.appendChild(style);
  }

  // 主初始化函数
  async function initializeIntegration() {
    try {
      console.log('🚀 开始初始化统一多模态系统集成...');

      await patchHandleAIParse();
      await enhanceResultHandling();
      enhanceUploadButtonFeedback();

      console.log('✅ 统一多模态系统集成完成');

      // 发布集成完成事件
      window.dispatchEvent(new CustomEvent('unifiedSystemReady', {
        detail: {
          hasUIController: !!window.uiController,
          hasProcessor: !!window.uiController?.getProcessor(),
          timestamp: Date.now()
        }
      }));

    } catch (error) {
      console.error('❌ 统一多模态系统集成失败:', error);
    }
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeIntegration);
  } else {
    // 延迟执行，确保其他脚本已加载
    setTimeout(initializeIntegration, 100);
  }

})();