// 预处理缓存：基于文件名和大小进行简单缓存
const preprocessCache = new Map();

// 生成缓存键
function getCacheKey(file, maxSide, quality) {
  return `${file.name}_${file.size}_${file.lastModified}_${maxSide}_${quality}`;
}

// 图像预处理：压缩尺寸、转成JPEG、输出纯base64数据（优化版）
export async function preprocessImageFile(file, maxSide = 1600, quality = 0.85) {
  const cacheKey = getCacheKey(file, maxSide, quality);
  
  // 检查缓存
  if (preprocessCache.has(cacheKey)) {
    console.log(`✅ [${new Date().toISOString().substr(11, 12)}] 使用预处理缓存: ${file.name} - 大小: ${Math.round(file.size/1024)}KB`);
    return preprocessCache.get(cacheKey);
  }
  
  const startTime = performance.now();
  console.log(`🔄 [${new Date().toISOString().substr(11, 12)}] 开始预处理图片: ${file.name} - 原始大小: ${Math.round(file.size/1024)}KB`);
  
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      img.onload = () => {
        try {
          let { width, height } = img;
          const scale = Math.min(1, maxSide / Math.max(width, height));
          
          // 如果不需要缩放，直接使用原始数据
          if (scale >= 1 && file.type === 'image/jpeg') {
            const result = { base64: reader.result.split(',')[1], mimeType: 'image/jpeg', width, height };
            preprocessCache.set(cacheKey, result);
            const elapsed = Math.round(performance.now() - startTime);
            const outputSize = Math.round(result.base64.length * 0.75 / 1024); // 估算输出大小
            console.log(`⚡ [${new Date().toISOString().substr(11, 12)}] 预处理完成(原尺寸): ${file.name} ${width}x${height} 用时${elapsed}ms - 输出: ${outputSize}KB`);
            resolve(result);
            return;
          }
          
          // 需要缩放处理
          const originalWidth = width, originalHeight = height;
          if (scale < 1) {
            width = Math.round(width * scale);
            height = Math.round(height * scale);
            console.log(`📐 [${new Date().toISOString().substr(11, 12)}] 缩放图片: ${originalWidth}x${originalHeight} → ${width}x${height} (${Math.round(scale*100)}%)`);
          } else {
            console.log(`📐 [${new Date().toISOString().substr(11, 12)}] 保持原尺寸: ${width}x${height}`);
          }
          
          const canvasStart = performance.now();
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          
          // 优化绘制性能
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          ctx.drawImage(img, 0, 0, width, height);
          
          const canvasTime = Math.round(performance.now() - canvasStart);
          console.log(`🎨 [${new Date().toISOString().substr(11, 12)}] Canvas绘制完成: 用时${canvasTime}ms`);
          
          // 转换并缓存结果
          const blobStart = performance.now();
          canvas.toBlob((blob) => {
            const blobTime = Math.round(performance.now() - blobStart);
            console.log(`📦 [${new Date().toISOString().substr(11, 12)}] Blob生成完成: 用时${blobTime}ms - 大小: ${Math.round(blob.size/1024)}KB`);
            
            const readerStart = performance.now();
            const blobReader = new FileReader();
            blobReader.onload = () => {
              const readerTime = Math.round(performance.now() - readerStart);
              const base64 = blobReader.result.split(',')[1];
              const result = { base64, mimeType: 'image/jpeg', width, height };
              
              // 缓存结果，但限制缓存大小
              if (preprocessCache.size > 20) {
                const firstKey = preprocessCache.keys().next().value;
                preprocessCache.delete(firstKey);
                console.log(`🧹 [${new Date().toISOString().substr(11, 12)}] 清理预处理缓存: 删除最旧条目`);
              }
              preprocessCache.set(cacheKey, result);
              
              const elapsed = Math.round(performance.now() - startTime);
              const outputSize = Math.round(result.base64.length * 0.75 / 1024);
              const compressionRatio = Math.round((1 - blob.size / file.size) * 100);
              
              console.log(`⚡ [${new Date().toISOString().substr(11, 12)}] 预处理完成: ${file.name} ${width}x${height} 用时${elapsed}ms`);
              console.log(`📊 [${new Date().toISOString().substr(11, 12)}] 压缩统计: ${Math.round(file.size/1024)}KB → ${Math.round(blob.size/1024)}KB (压缩${compressionRatio}%) - Base64: ${outputSize}KB`);
              
              resolve(result);
            };
            blobReader.onerror = reject;
            blobReader.readAsDataURL(blob);
          }, 'image/jpeg', quality);
          
        } catch (e) { reject(e); }
      };
      img.onerror = reject;
      img.src = reader.result;
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

// 简易图像类型判断（护照/机票/住宿），启发式
export function classifyImageName(name) {
  const lower = name.toLowerCase();
  if (/pass|passport|mrz/.test(lower)) return 'passport';
  if (/ticket|flight|boarding|itinerary/.test(lower)) return 'ticket';
  if (/hotel|inn|stay|booking|reservation/.test(lower)) return 'accommodation';
  return 'unknown';
}

// 生成多模态提示词片段
export function buildVisionInstruction(existingData) {
  return `你是MDAC资料抽取助手。输出JSON: {
  "fields": {
     fieldName: { "value": string|null, "confidence": 0-1, "sourceType": "passport|ticket|accommodation|unknown|mrz", "sourceFile": string }
  },
  "docTypeHints": [...],
  "warnings": []
}
仅限字段: name, passNo, dob, nationality, sex, passExpDte, email, region, mobile, arrDt, depDt, vesselNm, trvlMode, accommodationStay, accommodationAddress1, accommodationAddress2, accommodationState, accommodationCity, accommodationPostcode.
规则: 
1. 不虚构；无法确定用 null 并 confidence=0.
2. 日期统一DD/MM/YYYY。
3. 性别 M/F 转换为 1(男)/2(女)。
4. 若机票含航班号，如 MH123 / AK1234；住宿含邮编及城市；护照中MRZ优先。
5. 不要输出多余字段。
已知(仅帮助理解, 不直接覆盖): ${JSON.stringify(existingData || {})}`;
}
