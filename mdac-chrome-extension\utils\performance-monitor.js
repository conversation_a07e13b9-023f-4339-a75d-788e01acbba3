// MDAC性能监控工具
// 依赖：无外部依赖
// 技术栈：纯JavaScript性能监控
// 相关文件：所有需要性能统计的模块
// 核心功能：详细的时间戳记录、耗时统计、性能报告生成

class PerformanceMonitor {
    constructor() {
        this.sessions = new Map(); // 会话级监控
        this.globalStats = {
            totalOperations: 0,
            averageTime: 0,
            lastOperation: null
        };
    }

    // 创建新的性能会话
    startSession(sessionId, description = '') {
        const session = {
            id: sessionId,
            description,
            startTime: performance.now(),
            startTimestamp: new Date().toISOString(),
            steps: [],
            totalDuration: null,
            endTime: null,
            endTimestamp: null
        };
        
        this.sessions.set(sessionId, session);
        this.log(`🚀 开始会话: ${sessionId}${description ? ` - ${description}` : ''}`);
        return session;
    }

    // 记录步骤开始
    startStep(sessionId, stepName, details = '') {
        const session = this.sessions.get(sessionId);
        if (!session) {
            console.warn(`❌ 未找到会话: ${sessionId}`);
            return null;
        }

        const step = {
            name: stepName,
            details,
            startTime: performance.now(),
            startTimestamp: new Date().toISOString(),
            endTime: null,
            endTimestamp: null,
            duration: null,
            subSteps: []
        };

        session.steps.push(step);
        this.log(`📍 ${sessionId} > 开始: ${stepName}${details ? ` (${details})` : ''}`);
        return step;
    }

    // 记录步骤结束
    endStep(sessionId, stepName, result = null) {
        const session = this.sessions.get(sessionId);
        if (!session) return null;

        const step = session.steps.find(s => s.name === stepName && s.endTime === null);
        if (!step) {
            console.warn(`❌ 未找到步骤: ${stepName} in ${sessionId}`);
            return null;
        }

        step.endTime = performance.now();
        step.endTimestamp = new Date().toISOString();
        step.duration = Math.round(step.endTime - step.startTime);
        step.result = result;

        const statusIcon = result && result.success === false ? '❌' : '✅';
        this.log(`${statusIcon} ${sessionId} > 完成: ${stepName} 用时${step.duration}ms`);
        return step;
    }

    // 记录子步骤（用于更细粒度的监控）
    recordSubStep(sessionId, parentStepName, subStepName, duration, details = '') {
        const session = this.sessions.get(sessionId);
        if (!session) return null;

        const parentStep = session.steps.find(s => s.name === parentStepName);
        if (!parentStep) return null;

        const subStep = {
            name: subStepName,
            details,
            duration: Math.round(duration),
            timestamp: new Date().toISOString()
        };

        parentStep.subSteps.push(subStep);
        this.log(`  ⚡ ${sessionId} > ${parentStepName} > ${subStepName}: ${subStep.duration}ms`);
        return subStep;
    }

    // 结束会话
    endSession(sessionId, summary = '') {
        const session = this.sessions.get(sessionId);
        if (!session) return null;

        session.endTime = performance.now();
        session.endTimestamp = new Date().toISOString();
        session.totalDuration = Math.round(session.endTime - session.startTime);

        // 更新全局统计
        this.globalStats.totalOperations++;
        this.globalStats.averageTime = 
            (this.globalStats.averageTime * (this.globalStats.totalOperations - 1) + session.totalDuration) / 
            this.globalStats.totalOperations;
        this.globalStats.lastOperation = session.endTimestamp;

        this.log(`🏁 完成会话: ${sessionId} 总耗时${session.totalDuration}ms${summary ? ` - ${summary}` : ''}`);
        this.generateReport(sessionId);
        
        return session;
    }

    // 生成性能报告
    generateReport(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) return null;

        const report = {
            session: {
                id: session.id,
                description: session.description,
                totalDuration: session.totalDuration,
                startTime: session.startTimestamp,
                endTime: session.endTimestamp
            },
            steps: session.steps.map(step => ({
                name: step.name,
                duration: step.duration,
                percentage: session.totalDuration ? Math.round((step.duration / session.totalDuration) * 100) : 0,
                details: step.details,
                subSteps: step.subSteps
            })),
            breakdown: this.getTimeBreakdown(session),
            recommendations: this.getRecommendations(session)
        };

        // 打印详细报告
        this.logReport(report);
        return report;
    }

    // 获取时间分解
    getTimeBreakdown(session) {
        const breakdown = {};
        let totalTracked = 0;

        session.steps.forEach(step => {
            if (step.duration) {
                breakdown[step.name] = {
                    duration: step.duration,
                    percentage: session.totalDuration ? Math.round((step.duration / session.totalDuration) * 100) : 0
                };
                totalTracked += step.duration;
            }
        });

        // 计算未追踪时间
        const untracked = session.totalDuration - totalTracked;
        if (untracked > 0) {
            breakdown['未追踪时间'] = {
                duration: untracked,
                percentage: Math.round((untracked / session.totalDuration) * 100)
            };
        }

        return breakdown;
    }

    // 生成优化建议
    getRecommendations(session) {
        const recommendations = [];
        const slowSteps = session.steps.filter(s => s.duration > 2000).sort((a, b) => b.duration - a.duration);

        if (slowSteps.length > 0) {
            recommendations.push(`最慢步骤: ${slowSteps[0].name} (${slowSteps[0].duration}ms)`);
        }

        if (session.totalDuration > 10000) {
            recommendations.push('总时间过长，建议优化并发处理');
        }

        const apiSteps = session.steps.filter(s => s.name.includes('API') || s.name.includes('模型'));
        if (apiSteps.length > 0) {
            const apiTime = apiSteps.reduce((sum, s) => sum + (s.duration || 0), 0);
            const apiPercentage = Math.round((apiTime / session.totalDuration) * 100);
            recommendations.push(`API调用占比: ${apiPercentage}% (${apiTime}ms)`);
        }

        return recommendations;
    }

    // 打印详细报告
    logReport(report) {
        console.group(`📊 性能报告: ${report.session.id}`);
        console.log(`总耗时: ${report.session.totalDuration}ms`);
        console.log(`开始时间: ${report.session.startTime}`);
        console.log(`结束时间: ${report.session.endTime}`);
        
        console.group('📈 步骤分解:');
        report.steps.forEach(step => {
            console.log(`${step.name}: ${step.duration}ms (${step.percentage}%)`);
            if (step.subSteps.length > 0) {
                step.subSteps.forEach(sub => {
                    console.log(`  └─ ${sub.name}: ${sub.duration}ms`);
                });
            }
        });
        console.groupEnd();

        if (report.recommendations.length > 0) {
            console.group('💡 优化建议:');
            report.recommendations.forEach(rec => console.log(`• ${rec}`));
            console.groupEnd();
        }

        console.groupEnd();
    }

    // 获取会话信息
    getSession(sessionId) {
        return this.sessions.get(sessionId);
    }

    // 清理旧会话（防止内存泄漏）
    cleanup(maxSessions = 50) {
        if (this.sessions.size <= maxSessions) return;

        const sessions = Array.from(this.sessions.entries());
        sessions.sort((a, b) => (a[1].endTime || 0) - (b[1].endTime || 0));
        
        const toDelete = sessions.slice(0, sessions.length - maxSessions);
        toDelete.forEach(([id]) => this.sessions.delete(id));
        
        this.log(`🧹 清理了${toDelete.length}个旧会话`);
    }

    // 统一日志输出
    log(message) {
        const timestamp = new Date().toISOString().substr(11, 12); // HH:mm:ss.sss
        console.log(`[${timestamp}] ${message}`);
    }

    // 获取全局统计
    getGlobalStats() {
        return { ...this.globalStats };
    }

    // 重置全局统计
    resetGlobalStats() {
        this.globalStats = {
            totalOperations: 0,
            averageTime: 0,
            lastOperation: null
        };
    }
}

// 创建全局实例
window.performanceMonitor = new PerformanceMonitor();

// 导出供其他模块使用
export default window.performanceMonitor;